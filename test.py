import pyautogui  # 操作窗体
from pynput.keyboard import Key, Controller  # 操作键盘
import time

# goojurfunpbqchac
def send_message(message):
    pyautogui.hotkey('ctrl', 'alt', 'w')
    time.sleep(1)

    keyboard = Controller()
    keyboard.type(message)
    keyboard.press(Key.enter)
    keyboard.release(Key.enter)

    pyautogui.hotkey('ctrl', 'alt', 'w')


for i in range(100):
    send_message("nihao")
