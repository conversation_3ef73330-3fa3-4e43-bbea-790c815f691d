import{_ as A,u as B,r as c,a as I,o as S,b as d,d as i,e as l,F as j,j as N,k as _,t as a,h as C,w as b,v as k,f as q,i as y}from"./index-05e9acf1.js";import{u as U}from"./request-7c49b76d.js";const O={name:"UrlManager",setup(){const p=B(),e=c(!0),u=c([]),t=c(!1),v=c(!1),m=c(!1),o=c(!1),r=c(null),g=c(!1),n=I({id:null,url:"",type:"",Category:"",detail_url:""}),L=async()=>{e.value=!0;try{const s=await U.getUrls();u.value=s.data||[]}catch(s){console.error("获取URL列表失败",s),alert("获取URL列表失败："+(s.message||"未知错误"))}finally{e.value=!1}},M=()=>{p.push("/")},R=()=>{v.value=!1,Object.keys(n).forEach(s=>{n[s]=s==="id"?null:""}),t.value=!0},D=s=>{v.value=!0,Object.keys(n).forEach(f=>{n[f]=s[f]||""}),t.value=!0},h=()=>{m.value||(t.value=!1)},w=async()=>{if(!n.url.trim()){alert("请输入URL");return}if(n.type!=="智联"&&!n.Category){alert("请输入分类编号");return}n.type==="智联"&&(n.Category="",n.detail_url=""),m.value=!0;try{let s;if(v.value){s=await U.updateUrl(n.id,n);const f=u.value.findIndex(V=>V.id===n.id);f!==-1&&(u.value[f]=s.data),alert("URL更新成功")}else s=await U.addUrl(n),u.value.unshift(s.data),alert("URL添加成功");t.value=!1}catch(s){console.error("保存URL失败",s),alert("保存失败："+(s.message||"未知错误"))}finally{m.value=!1}},F=s=>{r.value=s,o.value=!0},x=()=>{o.value=!1,r.value=null},E=async()=>{if(!(!r.value||!r.value.id)){g.value=!0;try{await U.deleteUrl(r.value.id),u.value=u.value.filter(s=>s.id!==r.value.id),alert("URL删除成功"),o.value=!1,r.value=null}catch(s){console.error("删除URL失败",s),alert("删除失败："+(s.message||"未知错误"))}finally{g.value=!1}}};return S(()=>{L()}),{loading:e,urlList:u,showModal:t,isEditing:v,isSaving:m,urlForm:n,showDeleteConfirm:o,deleteUrl:r,isDeleting:g,goBack:M,openAddModal:R,openEditModal:D,closeModal:h,saveUrl:w,confirmDelete:F,cancelDelete:x,deleteUrlItem:E}}},T={class:"url-manager-container"},z={class:"page-header"},G={class:"content-panel"},H={class:"panel-header"},J={class:"table-wrapper"},K={key:0,class:"data-table"},P={class:"url-cell"},Q={class:"url-cell"},W={class:"action-buttons"},X=["onClick"],Y=["onClick"],Z={key:1,class:"loading-placeholder"},$={key:2,class:"empty-placeholder"},ll={class:"url-modal-content"},el={class:"url-modal-header"},tl={class:"url-modal-body"},ol={class:"form-group"},sl={class:"form-group"},nl={key:0,class:"form-group"},al={key:1,class:"form-group"},rl={class:"form-actions"},dl=["disabled"],il={class:"confirm-modal-content"},ul={class:"confirm-modal-body"},cl={class:"url-preview"},vl={class:"confirm-actions"},ml=["disabled"];function fl(p,e,u,t,v,m){return d(),i("div",T,[l("header",z,[e[14]||(e[14]=l("h1",null,"URL管理系统",-1)),e[15]||(e[15]=l("p",{class:"sub-title"},"管理岗位爬取链接",-1)),l("button",{class:"back-btn",onClick:e[0]||(e[0]=(...o)=>t.goBack&&t.goBack(...o))},"返回岗位列表")]),l("section",G,[l("div",H,[e[16]||(e[16]=l("h2",null,"URL列表",-1)),l("button",{class:"add-btn",onClick:e[1]||(e[1]=(...o)=>t.openAddModal&&t.openAddModal(...o))},"新增URL")]),l("div",J,[t.urlList.length>0?(d(),i("table",K,[e[17]||(e[17]=l("thead",null,[l("tr",null,[l("th",null,"ID"),l("th",null,"URL"),l("th",null,"类型"),l("th",null,"请求参数类型"),l("th",null,"详情地址"),l("th",null,"创建时间"),l("th",null,"更新时间"),l("th",null,"操作")])],-1)),l("tbody",null,[(d(!0),i(j,null,N(t.urlList,o=>(d(),i("tr",{key:o.id},[l("td",null,a(o.id),1),l("td",P,a(o.url),1),l("td",null,a(o.type||"-"),1),l("td",null,a(o.Category),1),l("td",Q,a(o.detail_url||"-"),1),l("td",null,a(o.created_at),1),l("td",null,a(o.updated_at),1),l("td",null,[l("div",W,[l("button",{class:"edit-btn",onClick:r=>t.openEditModal(o)},"编辑",8,X),l("button",{class:"delete-btn",onClick:r=>t.confirmDelete(o)},"删除",8,Y)])])]))),128))])])):t.loading?(d(),i("div",Z,e[18]||(e[18]=[l("div",{class:"spinner"},null,-1),l("p",null,"加载中...",-1)]))):(d(),i("div",$,[e[19]||(e[19]=l("p",null,"暂无URL数据",-1)),l("button",{class:"add-btn",onClick:e[2]||(e[2]=(...o)=>t.openAddModal&&t.openAddModal(...o))},"添加第一个URL")]))])]),t.showModal?(d(),i("div",{key:0,class:"url-modal",onClick:e[10]||(e[10]=_((...o)=>t.closeModal&&t.closeModal(...o),["self"]))},[l("div",ll,[l("button",{class:"close-btn",onClick:e[3]||(e[3]=(...o)=>t.closeModal&&t.closeModal(...o))},"×"),l("div",el,[l("h3",null,a(t.isEditing?"编辑URL":"新增URL"),1)]),l("div",tl,[l("div",ol,[e[20]||(e[20]=l("label",null,[C("URL "),l("span",{class:"required"},"*")],-1)),b(l("input",{type:"text","onUpdate:modelValue":e[4]||(e[4]=o=>t.urlForm.url=o),placeholder:"请输入URL"},null,512),[[k,t.urlForm.url]]),e[21]||(e[21]=l("div",{class:"help-text"},"例如: https://example.com",-1))]),l("div",sl,[e[23]||(e[23]=l("label",null,"类型",-1)),b(l("select",{"onUpdate:modelValue":e[5]||(e[5]=o=>t.urlForm.type=o),class:"select-input"},e[22]||(e[22]=[l("option",{value:"北森"},"北森",-1),l("option",{value:"智联"},"智联",-1)]),512),[[q,t.urlForm.type]])]),t.urlForm.type!=="智联"?(d(),i("div",nl,[e[24]||(e[24]=l("label",null,[C("请求参数类型 "),l("span",{class:"required"},"*")],-1)),b(l("input",{type:"number","onUpdate:modelValue":e[6]||(e[6]=o=>t.urlForm.Category=o),placeholder:"请输入分类编号"},null,512),[[k,t.urlForm.Category]])])):y("",!0),t.urlForm.type!=="智联"?(d(),i("div",al,[e[25]||(e[25]=l("label",null,"详情地址",-1)),b(l("input",{type:"text","onUpdate:modelValue":e[7]||(e[7]=o=>t.urlForm.detail_url=o),placeholder:"请输入详情地址"},null,512),[[k,t.urlForm.detail_url]])])):y("",!0),l("div",rl,[l("button",{class:"cancel-btn",onClick:e[8]||(e[8]=(...o)=>t.closeModal&&t.closeModal(...o))},"取消"),l("button",{class:"save-btn",onClick:e[9]||(e[9]=(...o)=>t.saveUrl&&t.saveUrl(...o)),disabled:t.isSaving},a(t.isSaving?"保存中...":"保存"),9,dl)])])])])):y("",!0),t.showDeleteConfirm?(d(),i("div",{key:1,class:"confirm-modal",onClick:e[13]||(e[13]=_((...o)=>t.cancelDelete&&t.cancelDelete(...o),["self"]))},[l("div",il,[e[27]||(e[27]=l("div",{class:"confirm-modal-header"},[l("h3",null,"确认删除")],-1)),l("div",ul,[e[26]||(e[26]=l("p",{class:"confirm-message"},"确定要删除以下URL吗？此操作不可撤销。",-1)),l("div",cl,a(t.deleteUrl?t.deleteUrl.url:""),1)]),l("div",vl,[l("button",{class:"cancel-btn",onClick:e[11]||(e[11]=(...o)=>t.cancelDelete&&t.cancelDelete(...o))},"取消"),l("button",{class:"delete-btn",onClick:e[12]||(e[12]=(...o)=>t.deleteUrlItem&&t.deleteUrlItem(...o)),disabled:t.isDeleting},a(t.isDeleting?"删除中...":"确认删除"),9,ml)])])])):y("",!0)])}const Ul=A(O,[["render",fl],["__scopeId","data-v-f5e90d92"]]);export{Ul as default};
