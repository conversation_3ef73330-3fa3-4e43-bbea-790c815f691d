#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ邮箱Excel文件下载器 - 简化版
基于imapclient库，支持多个QQ邮箱下载Excel文件
"""

import os
import re
import json
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from email.header import decode_header
import email

try:
    from imapclient import IMAPClient
except ImportError:
    print("❌ 缺少依赖库 imapclient")
    print("💡 请运行: pip install imapclient")
    exit(1)

# ==================== 配置管理 ====================
CONFIG_FILE = "email_config.json"

DEFAULT_CONFIG = {
    "accounts": [],
    "download_folder": "downloads",
    "search_days": 30,
    "max_threads": 3
}

# 线程锁和计数器
download_lock = threading.Lock()
total_downloaded = 0

def load_config():
    """加载配置文件"""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            pass
    return DEFAULT_CONFIG.copy()

def save_config(config):
    """保存配置文件"""
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except:
        return False

def add_account():
    """添加邮箱账号"""
    print("\n📝 添加QQ邮箱账号")
    print("=" * 40)

    config = load_config()

    email = input("请输入QQ邮箱: ").strip()
    if not email or not email.endswith("@qq.com"):
        print("❌ 请输入正确的QQ邮箱")
        return False

    # 检查是否已存在
    for account in config["accounts"]:
        if account["email"] == email:
            print("❌ 该邮箱已存在")
            return False

    password = input("请输入QQ邮箱授权码: ").strip()
    if not password:
        print("❌ 授权码不能为空")
        return False

    config["accounts"].append({"email": email, "password": password})

    if save_config(config):
        print("✅ 邮箱账号添加成功！")
        return True
    else:
        print("❌ 保存失败")
        return False

def remove_account():
    """删除邮箱账号"""
    config = load_config()

    if not config["accounts"]:
        print("❌ 没有配置的邮箱账号")
        return False

    print("\n📝 删除邮箱账号")
    print("=" * 40)

    for i, account in enumerate(config["accounts"], 1):
        print(f"{i}. {account['email']}")

    try:
        choice = int(input("\n请选择要删除的账号序号: ").strip())
        if 1 <= choice <= len(config["accounts"]):
            removed = config["accounts"].pop(choice - 1)
            if save_config(config):
                print(f"✅ 已删除邮箱: {removed['email']}")
                return True
            else:
                print("❌ 保存失败")
                return False
        else:
            print("❌ 无效的序号")
            return False
    except ValueError:
        print("❌ 请输入有效的数字")
        return False

def update_settings():
    """更新其他设置"""
    print("\n📝 更新设置")
    print("=" * 40)

    config = load_config()

    days = input(f"搜索天数范围 (当前{config['search_days']}天): ").strip()
    if days and days.isdigit():
        config["search_days"] = int(days)

    folder = input(f"下载文件夹 (当前{config['download_folder']}): ").strip()
    if folder:
        config["download_folder"] = folder

    threads = input(f"最大线程数 (当前{config['max_threads']}): ").strip()
    if threads and threads.isdigit():
        config["max_threads"] = int(threads)

    if save_config(config):
        print("✅ 设置更新成功！")
        return True
    else:
        print("❌ 保存失败")
        return False

# ==================== 辅助函数 ====================

def decode_filename(encoded_name):
    """解码文件名"""
    if not encoded_name:
        return ""

    try:
        decoded = decode_header(encoded_name)[0]
        if isinstance(decoded[0], bytes):
            encodings = [decoded[1], 'utf-8', 'gbk', 'gb2312', 'latin-1']
            for encoding in encodings:
                if encoding:
                    try:
                        return decoded[0].decode(encoding)
                    except (UnicodeDecodeError, LookupError):
                        continue
            return decoded[0].decode('utf-8', errors='ignore')
        return decoded[0]
    except Exception:
        return encoded_name

def is_excel_file(filename):
    """判断是否为Excel文件"""
    excel_extensions = ['.xlsx', '.xls', '.xlsm', '.xlsb']
    return any(filename.lower().endswith(ext) for ext in excel_extensions)

def make_safe_filename(filename):
    """生成安全的文件名"""
    illegal_chars = r'[<>:"/\\|?*]'
    safe_name = re.sub(illegal_chars, '_', filename)

    if len(safe_name) > 100:
        safe_name = safe_name[:100]

    safe_name = safe_name.strip('. ')
    return safe_name or "unnamed"

# ==================== 下载功能 ====================

def download_from_single_account(account, config):
    """从单个邮箱账号下载Excel文件"""
    global total_downloaded

    email_addr = account["email"]
    password = account["password"]
    download_folder = Path(config["download_folder"])
    search_days = config["search_days"]

    downloaded_count = 0

    try:
        print(f"🔗 [{email_addr}] 正在连接...")

        # 使用imapclient连接QQ邮箱
        with IMAPClient(host='imap.qq.com', port=993, ssl=True) as client:
            client.login(email_addr, password)
            client.select_folder('INBOX')

            print(f"✅ [{email_addr}] 连接成功")

            # 构建搜索条件
            since_date = (datetime.now() - timedelta(days=search_days)).strftime("%d-%b-%Y")
            search_criteria = ['SINCE', since_date]

            # 搜索邮件
            messages = client.search(search_criteria)
            print(f"📧 [{email_addr}] 找到 {len(messages)} 封邮件")

            # 创建邮箱专用文件夹
            email_prefix = email_addr.split('@')[0]
            account_folder = download_folder / email_prefix
            account_folder.mkdir(parents=True, exist_ok=True)

            # 处理每封邮件
            for i, msg_id in enumerate(messages, 1):
                try:
                    # 获取邮件数据
                    msg_data = client.fetch([msg_id], ['RFC822'])[msg_id]
                    raw_email = msg_data[b'RFC822']
                    parsed_email = email.message_from_bytes(raw_email)

                    # 处理附件
                    for part in parsed_email.walk():
                        if part.get_content_maintype() == 'multipart':
                            continue

                        content_disposition = part.get('Content-Disposition')
                        if content_disposition is None:
                            continue

                        filename = part.get_filename()
                        if not filename:
                            continue

                        # 解码文件名
                        decoded_filename = decode_filename(filename)

                        # 检查是否为Excel文件
                        if is_excel_file(decoded_filename):
                            # 生成安全的文件名
                            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
                            safe_filename = make_safe_filename(decoded_filename)
                            final_filename = f"{timestamp}_{safe_filename}"

                            # 保存文件
                            save_path = account_folder / final_filename

                            try:
                                with open(save_path, 'wb') as f:
                                    f.write(part.get_payload(decode=True))

                                downloaded_count += 1
                                with download_lock:
                                    total_downloaded += 1

                                print(f"📥 [{email_addr}] 下载: {decoded_filename}")

                            except Exception as e:
                                print(f"⚠️ [{email_addr}] 保存文件失败: {e}")

                except Exception as e:
                    continue

            print(f"✅ [{email_addr}] 完成，下载了 {downloaded_count} 个文件")
            return downloaded_count

    except Exception as e:
        print(f"❌ [{email_addr}] 连接错误: {e}")
        return 0

def download_excel_from_all_accounts():
    """从所有配置的邮箱账号下载Excel文件"""
    global total_downloaded
    total_downloaded = 0

    # 加载配置
    config = load_config()

    # 检查配置
    if not config["accounts"]:
        print("❌ 没有配置的邮箱账号！")
        print("💡 请先添加邮箱账号")
        return

    # 创建下载文件夹
    download_folder = config["download_folder"]
    if not os.path.exists(download_folder):
        os.makedirs(download_folder)
        print(f"📁 创建下载文件夹: {download_folder}")

    print(f"🚀 开始多线程下载，共 {len(config['accounts'])} 个邮箱账号")
    print(f"🧵 最大线程数: {config['max_threads']}")
    print("-" * 50)

    start_time = time.time()

    # 使用线程池执行下载
    with ThreadPoolExecutor(max_workers=config["max_threads"]) as executor:
        # 提交所有下载任务
        future_to_account = {
            executor.submit(download_from_single_account, account, config): account
            for account in config["accounts"]
        }

        # 等待所有任务完成
        for future in as_completed(future_to_account):
            account = future_to_account[future]
            try:
                result = future.result()
            except Exception as e:
                print(f"❌ [{account['email']}] 任务异常: {e}")

    end_time = time.time()
    elapsed_time = end_time - start_time

    print("\n" + "=" * 50)
    print(f"🎉 所有下载任务完成！")
    print(f"📊 总共下载: {total_downloaded} 个Excel文件")
    print(f"⏱️  用时: {elapsed_time:.2f} 秒")
    print(f"📁 文件保存在: {os.path.abspath(download_folder)}")
    print("=" * 50)

def show_config():
    """显示当前配置"""
    config = load_config()
    print("\n📋 当前配置信息")
    print("=" * 40)
    print(f"邮箱账号数量: {len(config['accounts'])}")

    if config['accounts']:
        print("已配置的邮箱:")
        for i, account in enumerate(config['accounts'], 1):
            print(f"  {i}. {account['email']}")
    else:
        print("  暂无配置的邮箱")

    print(f"下载文件夹: {config['download_folder']}")
    print(f"搜索天数: {config['search_days']}天")
    print(f"最大线程数: {config['max_threads']}")

def show_menu():
    """显示主菜单"""
    print("\n" + "=" * 50)
    print("🔧 QQ邮箱Excel文件下载器 - 多线程版")
    print("=" * 50)
    print("1. 添加邮箱账号")
    print("2. 删除邮箱账号")
    print("3. 开始下载Excel文件")
    print("4. 查看当前配置")
    print("5. 更新设置")
    print("6. 退出程序")
    print("-" * 50)

def main():
    """主函数"""
    print("💡 获取QQ邮箱授权码方法：")
    print("   1. 登录QQ邮箱网页版")
    print("   2. 设置 → 账户 → POP3/IMAP/SMTP服务")
    print("   3. 开启IMAP/SMTP服务并生成授权码")

    while True:
        show_menu()
        choice = input("请选择操作 (1-6): ").strip()

        if choice == '1':
            add_account()
        elif choice == '2':
            remove_account()
        elif choice == '3':
            config = load_config()
            if not config["accounts"]:
                print("❌ 请先添加邮箱账号！")
                continue

            print(f"\n📧 邮箱账号: {len(config['accounts'])} 个")
            print(f"📅 搜索范围: 最近{config['search_days']}天")
            print(f"📁 下载目录: {config['download_folder']}")
            print(f"🧵 最大线程数: {config['max_threads']}")

            confirm = input("\n确认开始多线程下载？(y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是', '确认']:
                download_excel_from_all_accounts()
            else:
                print("❌ 取消下载")
        elif choice == '4':
            show_config()
        elif choice == '5':
            update_settings()
        elif choice == '6':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请输入1-6")

        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
