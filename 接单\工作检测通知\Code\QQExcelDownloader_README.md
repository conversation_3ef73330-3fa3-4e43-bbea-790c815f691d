# QQ邮箱Excel文件下载器使用说明

## 功能介绍
`QQExcelDownloader` 是一个专门用于从QQ邮箱下载Excel附件的执行器，支持多种筛选条件和自动分类存储。

## 主要特性
- 支持多个QQ邮箱账号
- 按发件人筛选邮件
- 按主题关键词筛选邮件
- 按时间范围筛选邮件（默认7天内）
- 自动解码中文文件名
- 按发件人自动分类存储
- 生成带时间戳的安全文件名
- 支持调试模式

## 配置参数说明

### 必需参数
- `output_directory_path`: 输出目录路径
- `qq_email_configs`: QQ邮箱配置列表

### 可选参数
- `sender_filters`: 发件人过滤列表（只下载指定发件人的邮件）
- `subject_keywords`: 主题关键词列表（只处理包含这些关键词的邮件）
- `since_days`: 下载多少天内的邮件（默认7天）
- `is_debug`: 是否为调试模式（默认False）

## QQ邮箱授权码设置

1. 登录QQ邮箱网页版
2. 点击"设置" -> "账户"
3. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
4. 开启"IMAP/SMTP服务"
5. 生成授权码（不是QQ密码）
6. 在配置中使用此授权码作为password

## 配置示例

```json
{
  "type": "QQExcelDownloader",
  "output_directory_path": "./downloads/qq_excel",
  "qq_email_configs": [
    {
      "username": "<EMAIL>",
      "password": "your_authorization_code"
    }
  ],
  "sender_filters": [
    "<EMAIL>",
    "<EMAIL>"
  ],
  "subject_keywords": [
    "岗位",
    "招聘",
    "职位"
  ],
  "since_days": 7
}
```

## 输出文件结构

```
downloads/qq_excel/
├── sender1/
│   ├── 20250525_120000_邮件主题_1.xlsx
│   └── 20250525_130000_另一个主题_1.xlsx
└── sender2/
    └── 20250525_140000_招聘信息_1.xlsx
```

## 支持的Excel格式
- .xlsx (Excel 2007+)
- .xls (Excel 97-2003)
- .xlsm (Excel 宏启用工作簿)
- .xlsb (Excel 二进制工作簿)

## 使用方法

### 1. 单独运行
```python
from QQExcelDownloader import QQExcelDownloader

downloader = QQExcelDownloader(
    output_directory_path="./downloads",
    qq_email_configs=[
        {
            "username": "<EMAIL>",
            "password": "your_auth_code"
        }
    ],
    sender_filters=["<EMAIL>"],
    subject_keywords=["招聘", "岗位"],
    since_days=7
)

downloader.execute()
```

### 2. 通过配置文件运行
将配置添加到主配置文件的 `Executors` 数组中，然后运行主程序：

```bash
python main.py --configPath=your_config.json
```

## 注意事项

1. **授权码安全**: 请妥善保管QQ邮箱授权码，不要泄露给他人
2. **网络连接**: 确保网络连接稳定，QQ邮箱IMAP服务器为 `imap.qq.com:993`
3. **文件名长度**: 自动生成的文件名会被限制在合理长度内
4. **中文支持**: 完全支持中文邮件主题和附件文件名
5. **调试模式**: 调试模式下只会处理第一封符合条件的邮件

## 错误处理

- 网络连接失败：自动重试机制
- 邮件解析错误：跳过有问题的邮件，继续处理其他邮件
- 文件保存错误：记录错误日志，不影响其他文件下载
- 编码问题：支持多种中文编码格式自动检测
