import axios from 'axios'

const service = axios.create({
  baseURL: 'http://************:5000', 
  // baseURL: 'http://localhost:5000', 
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
})

service.interceptors.request.use(
  config => {

    return config
  },
  error => {
    console.error('请求错误：', error)
    return Promise.reject(error)
  }
)
service.interceptors.response.use(
  response => {
    const res = response.data
    
    if (res.code !== 200) {
      console.error('接口错误：', res.message || '未知错误')
      return Promise.reject(new Error(res.message || '未知错误'))
    } else {
      return res
    }
  },
  error => {
    console.error('响应错误：', error)
    return Promise.reject(error)
  }
)

// API封装
export const jobApi = {
  // 获取工作列表
  getJobs(params) {
    return service({
      url: '/api/jobs',
      method: 'get',
      params
    })
  },
  
  // 获取工作详情
  getJobDetail(id) {
    return service({
      url: `/api/jobs/${id}`,
      method: 'get'
    })
  },
  
  // 更新岗位信息
  updateJob(id, data) {
    return service({
      url: `/api/jobs/${id}`,
      method: 'put',
      data
    })
  },
  
  // 获取备用链接域名列表
  getBackupLinks() {
    return service({
      url: '/api/backup_links',
      method: 'get'
    })
  },
  
  // 根据URL更新岗位名称
  updateJobNameByUrl(data) {
    return service({
      url: '/api/update_job_name_by_url',
      method: 'post',
      data
    })
  }
}

// URL管理API
export const urlApi = {
  // 获取所有URL
  getUrls() {
    return service({
      url: '/api/urls',
      method: 'get'
    })
  },
  
  // 更新URL
  updateUrl(id, data) {
    return service({
      url: `/api/urls/${id}`,
      method: 'put',
      data
    })
  },
  
  // 新增URL
  addUrl(data) {
    return service({
      url: '/api/urls',
      method: 'post',
      data
    })
  },
  
  // 删除URL
  deleteUrl(id) {
    return service({
      url: `/api/urls/${id}`,
      method: 'delete'
    })
  }
}

export default service 