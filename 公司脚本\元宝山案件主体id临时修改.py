import pandas as pd
import pymysql

# 读取Excel文件
df = pd.read_excel(r'C:\Users\<USER>\Desktop\aa.xlsx', engine='openpyxl', header=None)

# 读取第1列的数据
first_column = df.iloc[:, 0]
new = []
for i in first_column:
    i = i.replace("\n", "").replace("'", "")
    new.append(i)
print(new)

# 数据库连接配置
db_config = {
    "host": "************",
    "user": "user_xzcf",
    "password": "Nmgyjj@2022",
    "database": "lcm_project",
    "port": 3306,
    "charset": "utf8"
}

try:
    with pymysql.connect(**db_config) as connection:
        with connection.cursor() as cursor:
            for i in new:
                sql = f"SELECT case_id FROM t_push_case_info_task WHERE CASE_NO = '{i}'"
                cursor.execute(sql)
                result = cursor.fetchone()

                if result:
                    case_id = result[0]
                    sql = f"UPDATE t_push_judicial_case_info SET BELONG_MB_ID = '03b394ff-20b5-4df0-932a-5a1792943562' WHERE CASE_ID = '{case_id}'"
                    cursor.execute(sql)

                    sql = f"SELECT * FROM t_push_judicial_case_info WHERE CASE_ID = '{case_id}'"
                    cursor.execute(sql)
                    print(cursor.fetchone())
                else:
                    print(f"No case_id found for CASE_NO: {i}")

            # 用户确认
            user_input = input("Enter '1' to commit changes, any other key to rollback: ")
            if user_input == "1":
                print("ok")
                connection.commit()
            else:
                connection.rollback()
                print("error")
except Exception as e:
    print(f"An error occurred: {e}")
    if 'connection' in locals():
        connection.rollback()
