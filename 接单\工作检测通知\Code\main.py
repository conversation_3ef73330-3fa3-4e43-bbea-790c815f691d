import argparse
import json
import logging
import time
import schedule
from tkinter import messagebox
from typing import List, Optional

from ExecutorBuilder import ExecutorBuilder
from Executor import Executor
from DebugLogger import DebugLogger

logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Jobridge")
    parser.add_argument('--configPath', type=str, default="./config.json", help="配置文件")
    parser.add_argument('--loggingLevel', type=int, default=logging.INFO, help="日志等級")
    parser.add_argument('--logFile', type=str, default=None, help="日志文件")
    parser.add_argument('--force', action='store_true', help="强制立即执行，忽略调度配置")
    parser.add_argument('--debug', action='store_true', help="是否是debug模式")
    return parser.parse_args()

def load_config(config_path: str) -> tuple[List[dict], Optional[str]]:
    """加载配置文件
    
    Returns:
        tuple: (执行器配置列表, 调度时间)
    """
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    executors_config = config.get("Executors", [])
    schedule_time = config.get("Schedule", {}).get("time")
    return executors_config, schedule_time

def run_executors(executors: List[Executor], debugLogger:DebugLogger, show_message: bool = True):
    """运行所有执行器"""
    if show_message:
        messagebox.showinfo(f"开始执行，请等待执行完毕提示！任务量：{len(executors)}")

    debugLogger.info("执行开始")
    for executor in executors:
        try:
            executor.execute()
        except Exception as e:
            debugLogger.error(f"执行器运行失败: {str(e)}")
            # 重新抛出异常，使其传播到main函数并触发重启机制
            raise Exception(f"执行器运行失败，需要重启: {str(e)}")

    debugLogger.info("执行完毕")
    if show_message:
        messagebox.showinfo("执行完毕")

def main() -> None:
    is_restart = False  # 标记是否是重启后执行
    restart_count = 0

    while True:
        try:
            args = parse_args()
            logging.basicConfig(
                level=args.loggingLevel, 
                filename=args.logFile,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )

            # 加载配置
            with open(args.configPath, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 从配置中读取重启参数
            system_config = config.get("System", {})
            max_restarts = system_config.get("max_restarts", 3)
            restart_delay = system_config.get("restart_delay", 60)
            
            # 创建执行器
            executorBuilder = ExecutorBuilder()
            executors = executorBuilder.build(config, args.debug)
            schedule_time = config.get("Schedule", {}).get("time")
            debugLogger = DebugLogger(config.get("DebugLogger", {}).get("webhook_key"), logger)

            # 清除之前注册的所有定时任务，防止任务重复执行
            schedule.clear()
            
            # 添加整点报时功能
            for hour in range(9, 19):  # 从9点到18点
                schedule.every().day.at(f"{hour:02d}:00").do(
                    lambda h=hour: debugLogger.debug(f"现在是 {h:02d}:00,系统运行正常")
                )

            # 如果是重启后执行，先立即执行一次任务
            if is_restart:
                debugLogger.debug("重启后执行：立即运行一次任务")
                current_hour = time.localtime().tm_hour
                debugLogger.debug(f"重启后执行：现在是 {current_hour:02d}:{time.localtime().tm_min:02d}，系统运行正常")
                run_executors(executors, debugLogger, show_message=False)
                is_restart = False  # 重置重启标志
                restart_count = 0   # 重置重启次数计数器
                
            if schedule_time and not args.force:
                # 定时运行模式
                debugLogger.info(f"已设置定时运行，将在每天 {schedule_time} 执行")
                schedule.every().day.at(schedule_time).do(run_executors, executors, debugLogger, show_message=False)
                
                try:
                    while True:
                        schedule.run_pending()
                        time.sleep(60)  # 每分钟检查一次
                except KeyboardInterrupt:
                    debugLogger.info("程序已停止")
                    return  # 键盘中断时正常退出，不重启
            else:
                # 立即运行模式
                if not is_restart:  # 只有在非重启情况下才需要执行，因为重启情况已经执行过了
                    current_hour = time.localtime().tm_hour
                    debugLogger.debug(f"现在是 {current_hour:02d}:{time.localtime().tm_min:02d}，系统运行正常")
                    run_executors(executors, debugLogger, show_message=False)
                return  # 正常执行完成，不需要重启
                
        except Exception as e:
            restart_count += 1

            debugLogger.critical(f"程序遇到严重错误: {str(e)}, 这是第 {restart_count} 次重启")
            
            if restart_count <= max_restarts:
                debugLogger.warning(f"程序将在 {restart_delay} 秒后重启...")
                time.sleep(restart_delay)
                debugLogger.warning("正在重启程序...")
                is_restart = True  # 标记下一次循环是重启后执行
            else:
                debugLogger.critical(f"已达到最大重启次数 ({max_restarts})，程序终止", level=logging.CRITICAL)
                return  # 退出程序
    
if __name__ == '__main__':
    main()
