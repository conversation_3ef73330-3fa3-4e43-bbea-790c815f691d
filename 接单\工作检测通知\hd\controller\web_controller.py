from flask import Blueprint, request, jsonify
import logging
import re
from datetime import datetime
import pymysql


web = Blueprint('web', __name__)

def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(
            host='************',
            user='root',
            password='10001000',
            database='job_crawler',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
    except Exception as e:
        logging.error(f"数据库连接失败: {str(e)}")
        raise

@web.route('/api/jobs', methods=['GET'])
def get_jobs():
    """
    获取岗位列表，支持分页和所有参数查询
    """
    try:

        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 10))
        
        params_dict = {}
        for key, value in request.args.items():
            if key not in ['page', 'size']:
                params_dict[key] = value
        

        conn = get_db_connection()
        cursor = conn.cursor()
        

        where_clauses = []
        sql_params = []
        
        for key, value in params_dict.items():
            # 排序字段特殊处理
            if key == 'sort_field' or key == 'sort_order':
                continue
                
            if value:
                where_clauses.append(f"{key} LIKE %s")
                sql_params.append(f"%{value}%")

        where_sql = " AND ".join(where_clauses) if where_clauses else "1=1"

        sort_field = request.args.get('sort_field', 'id')
        sort_order = request.args.get('sort_order', 'desc')

        allowed_sort_fields = ['id', 'company_name', 'company_type', 'industry', 'position_name', 
                              'work_location', 'education_req', 'major_req', 'post_date', 'end_date',
                              'work_type', 'position_type', 'job_type']
        if sort_field not in allowed_sort_fields:
            sort_field = 'id'

        if sort_order.lower() not in ['asc', 'desc']:
            sort_order = 'desc'
        

        count_sql = f"SELECT COUNT(*) as total FROM jobs WHERE {where_sql}"
        cursor.execute(count_sql, sql_params)
        total = cursor.fetchone()['total']

        offset = (page - 1) * size
        query_sql = f"""
            SELECT * FROM jobs 
            WHERE {where_sql} 
            ORDER BY {sort_field} {sort_order}
            LIMIT %s OFFSET %s
        """
        cursor.execute(query_sql, sql_params + [size, offset])
        jobs = cursor.fetchall()
        
        # 格式化日期
        for job in jobs:
            if 'created_at' in job and job['created_at']:
                job['created_at'] = job['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if 'updated_at' in job and job['updated_at']:
                job['updated_at'] = job['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.close()
        conn.close()

        result = {
            'code': 200,
            'message': '获取成功',
            'data': {
                'total': total,
                'page': page,
                'size': size,
                'total_pages': (total + size - 1) // size,
                'jobs': jobs
            }
        }
        
        return jsonify(result)
        
    except Exception as e:
        logging.error(f"获取岗位信息失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取岗位信息失败: {str(e)}',
            'data': None
        })

@web.route('/api/jobs/<int:job_id>', methods=['PUT'])
def update_job(job_id):
    """
    修改特定岗位的信息，支持修改所有参数
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据为空',
                'data': None
            })
        

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM jobs WHERE id = %s", (job_id,))
        job = cursor.fetchone()
        
        if not job:
            cursor.close()
            conn.close()
            return jsonify({
                'code': 404,
                'message': f'未找到ID为{job_id}的岗位',
                'data': None
            })
        

        update_fields = []
        update_values = []
        

        allowed_fields = [
            'company_name', 'company_type', 'industry', 'position_name', 
            'work_location', 'education_req', 'major_req', 'salary', 
            'job_type', 'position_type', 'work_type', 'post_date', 'end_date',
            'position_link', 'backup_link', 'position_desc', 'position_requirement'
        ]
        

        for field in allowed_fields:
            if field in data:
                update_fields.append(f"{field} = %s")
                update_values.append(data[field])
        
        if not update_fields:
            cursor.close()
            conn.close()
            return jsonify({
                'code': 400,
                'message': '未提供有效的更新字段',
                'data': None
            })
        
        update_fields.append("updated_at = CURRENT_TIMESTAMP")
        
        sql = f"UPDATE jobs SET {', '.join(update_fields)} WHERE id = %s"
        update_values.append(job_id)
        
        cursor.execute(sql, update_values)
        conn.commit()
        
        cursor.execute("SELECT * FROM jobs WHERE id = %s", (job_id,))
        updated_job = cursor.fetchone()
        
        if updated_job:
            if 'created_at' in updated_job and updated_job['created_at']:
                updated_job['created_at'] = updated_job['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if 'updated_at' in updated_job and updated_job['updated_at']:
                updated_job['updated_at'] = updated_job['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'code': 200,
            'message': '更新成功',
            'data': updated_job
        })
        
    except Exception as e:
        logging.error(f"更新岗位信息失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'更新岗位信息失败: {str(e)}',
            'data': None
        })

@web.route('/api/backup_links', methods=['GET'])
def get_backup_links():
    """
    获取所有备用链接，只保留域名部分，分组去重后返回
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT backup_link FROM jobs WHERE backup_link IS NOT NULL AND backup_link != ''")
        links = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        domain_set = set()
        for link in links:
            url = link['backup_link']
            domain = re.search(r'https?://([^/]+)', url)
            if domain:
                domain_set.add(domain.group(1))
        
        domains = sorted(list(domain_set))
        
        result = {
            'code': 200,
            'message': '获取成功',
            'data': {
                'total': len(domains),
                'domains': domains
            }
        }
        
        return jsonify(result)
        
    except Exception as e:
        logging.error(f"获取备用链接失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取备用链接失败: {str(e)}',
            'data': None
        })

@web.route('/api/update_job_name_by_url', methods=['POST'])
def update_job_name_by_url():
    """
    根据URL更新岗位名称
    接收url和name参数，更新backup_link包含该url的所有岗位的名称
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据为空',
                'data': None
            })
        
        url = data.get('url')
        name = data.get('name')
        

        if not url or not name:
            return jsonify({
                'code': 400,
                'message': '缺少必要参数(url或name)',
                'data': None
            })
        

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute(
            "UPDATE jobs SET company_name = %s, updated_at = CURRENT_TIMESTAMP WHERE backup_link LIKE %s",
            (name, f"%{url}%")
        )
        
        affected_rows = cursor.rowcount
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({
            'code': 200,
            'message': '更新成功',
            'data': {
                'affected_rows': affected_rows
            }
        })
        
    except Exception as e:
        logging.error(f"更新岗位名称失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'更新岗位名称失败: {str(e)}',
            'data': None
        })

@web.route('/api/urls', methods=['GET'])
def get_urls():
    """
    获取所有URL记录
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        

        cursor.execute("SELECT * FROM urls ORDER BY id DESC")
        urls = cursor.fetchall()

        for url in urls:
            if 'created_at' in url and url['created_at']:
                url['created_at'] = url['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if 'updated_at' in url and url['updated_at']:
                url['updated_at'] = url['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': urls
        })
        
    except Exception as e:
        logging.error(f"获取URL列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取URL列表失败: {str(e)}',
            'data': None
        })

@web.route('/api/urls/<int:url_id>', methods=['PUT'])
def update_url(url_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据为空',
                'data': None
            })
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM urls WHERE id = %s", (url_id,))
        url = cursor.fetchone()
        
        if not url:
            cursor.close()
            conn.close()
            return jsonify({
                'code': 404,
                'message': f'未找到ID为{url_id}的URL记录',
                'data': None
            })
        
        update_fields = []
        update_values = []
        
        allowed_fields = ['url', 'type', 'Category', 'detail_url', 'status']
        for field in allowed_fields:
            if field in data:
                value = data[field]
                if field == 'Category':
                    if value in ['', None]:
                        value = 0
                    else:
                        value = int(value)
                update_fields.append(f"{field} = %s")
                update_values.append(value)
        
        if not update_fields:
            cursor.close()
            conn.close()
            return jsonify({
                'code': 400,
                'message': '未提供有效的更新字段',
                'data': None
            })
        
        sql = f"UPDATE urls SET {', '.join(update_fields)} WHERE id = %s"
        update_values.append(url_id)
        
        cursor.execute(sql, update_values)
        conn.commit()
        
        cursor.execute("SELECT * FROM urls WHERE id = %s", (url_id,))
        updated_url = cursor.fetchone()
        
        if updated_url:
            if 'created_at' in updated_url and updated_url['created_at']:
                updated_url['created_at'] = updated_url['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if 'updated_at' in updated_url and updated_url['updated_at']:
                updated_url['updated_at'] = updated_url['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'code': 200,
            'message': '更新成功',
            'data': updated_url
        })
        
    except Exception as e:
        logging.error(f"更新URL失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'更新URL失败: {str(e)}',
            'data': None
        })

@web.route('/api/urls', methods=['POST'])
def add_url():
    """
    新增URL记录，支持status字段
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据为空',
                'data': None
            })
        
        url = data.get('url', '')
        url_type = data.get('type', '')
        category = data.get('Category', 0)
        detail_url = data.get('detail_url', '')
        status = data.get('status', 1)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO urls (url, type, Category, detail_url, status) VALUES (%s, %s, %s, %s, %s)",
            (url, url_type, category, detail_url, status)
        )
        conn.commit()
        new_id = cursor.lastrowid

        cursor.execute("SELECT * FROM urls WHERE id = %s", (new_id,))
        new_url = cursor.fetchone()
        

        if new_url:
            if 'created_at' in new_url and new_url['created_at']:
                new_url['created_at'] = new_url['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if 'updated_at' in new_url and new_url['updated_at']:
                new_url['updated_at'] = new_url['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'code': 200,
            'message': '添加成功',
            'data': new_url
        })
        
    except Exception as e:
        logging.error(f"添加URL失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'添加URL失败: {str(e)}',
            'data': None
        })

@web.route('/api/urls/<int:url_id>', methods=['DELETE'])
def delete_url(url_id):
    """
    删除指定ID的URL记录
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM urls WHERE id = %s", (url_id,))
        url = cursor.fetchone()
        
        if not url:
            cursor.close()
            conn.close()
            return jsonify({
                'code': 404,
                'message': f'未找到ID为{url_id}的URL记录',
                'data': None
            })
        

        cursor.execute("DELETE FROM urls WHERE id = %s", (url_id,))
        conn.commit()
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'code': 200,
            'message': '删除成功',
            'data': {'id': url_id}
        })
        
    except Exception as e:
        logging.error(f"删除URL失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'删除URL失败: {str(e)}',
            'data': None
        })

@web.route('/', defaults={'path': ''}, methods=['OPTIONS'])
@web.route('/<path:path>', methods=['OPTIONS'])
def options_handler(path):
    response = jsonify({'status': 'ok'})
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
    return response 