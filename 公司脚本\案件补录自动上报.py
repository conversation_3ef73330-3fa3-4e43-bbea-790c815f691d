import time

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 1. 打开浏览器
driver = webdriver.Edge()

# 2. 打开网址
driver.get("http://219.159.12.100:8889")
# 等待页面打开
driver.implicitly_wait(30)
# 最大化窗口
driver.maximize_window()
# 3. 登录
driver.find_element(By.CLASS_NAME, "login-in").send_keys("admin")
driver.find_elements(By.CLASS_NAME, "login-in")[1].send_keys("NM_test@2023$%")
driver.find_element(By.CLASS_NAME, "btn-login").click()
driver.find_element(By.XPATH, '//*[@id="app"]/section/section/div[1]/div/div[1]/div/td/div/div/ul/div[6]/li').click()
driver.find_element(By.XPATH,
                    '/html/body/div[1]/section/section/div[1]/div/div[1]/div/td/div/div/ul/div[6]/li/ul/div[2]/li').click()
driver.find_element(By.XPATH, '//*[@id="main_id"]/div/div/div/section/header/table/tr/td/div/div/div[4]/select').click()
driver.find_element(By.XPATH,
                    '//*[@id="main_id"]/div/div/div/section/header/table/tr/td/div/div/div[4]/select/option[2]').click()
driver.find_element(By.XPATH,
                    '//*[@id="main_id"]/div/div/div/section/header/table/tr/td/div/div/div[5]/div/button[1]').click()
# 鼠标往下滑
js = "var q=document.documentElement.scrollTop=1100"
driver.execute_script(js)
time.sleep(1)
# 循环点击按钮
i = 0
wait = WebDriverWait(driver, 3)
while True:
    # 点击第一个按钮
    button1 = wait.until(EC.element_to_be_clickable((By.XPATH,
                                                     '/html/body/div[1]/section/section/div[2]/main/div/div/div/section/main/div[2]/div[1]/div[4]/div[2]/table/tbody/tr[1]/td[9]/div/button[2]')))
    button1.click()
    time.sleep(0.5)

    # 点击确定按钮
    confirm_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR,
                                                            'body > div.el-message-box__wrapper > div > div.el-message-box__btns > button.el-button.el-button--default.el-button--small.el-button--primary')))
    confirm_button.click()
    time.sleep(2)

    i = i + 1
    print(f"点击了{i}次")
    if i == 600:
        break
