#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ邮箱Excel文件下载器 - 多邮箱多线程版
支持多个QQ邮箱同时下载Excel文件
"""

import imaplib
import email
import os
import re
import json
import threading
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# ==================== 配置管理 ====================
CONFIG_FILE = "email_config.json"

DEFAULT_CONFIG = {
    "accounts": [],
    "download_folder": "downloads",
    "search_days": 30,
    "max_threads": 3
}

def load_config():
    """加载配置文件"""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            pass
    return DEFAULT_CONFIG.copy()

def save_config(config):
    """保存配置文件"""
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except:
        return False

def add_account():
    """添加邮箱账号"""
    print("\n📝 添加QQ邮箱账号")
    print("=" * 40)

    config = load_config()

    email_addr = input("请输入QQ邮箱: ").strip()
    if not email_addr or not email_addr.endswith("@qq.com"):
        print("❌ 请输入正确的QQ邮箱")
        return False

    # 检查是否已存在
    for account in config["accounts"]:
        if account["email"] == email_addr:
            print("❌ 该邮箱已存在")
            return False

    password = input("请输入QQ邮箱授权码: ").strip()
    if not password:
        print("❌ 授权码不能为空")
        return False

    name = input("请输入账号备注名称 (可选): ").strip()
    if not name:
        name = email_addr.split("@")[0]

    account = {
        "email": email_addr,
        "password": password,
        "name": name,
        "enabled": True
    }

    config["accounts"].append(account)

    if save_config(config):
        print(f"✅ 账号 {name} ({email_addr}) 添加成功！")
        return True
    else:
        print("❌ 账号保存失败")
        return False

def manage_accounts():
    """管理邮箱账号"""
    config = load_config()

    while True:
        print("\n📧 邮箱账号管理")
        print("=" * 40)

        if not config["accounts"]:
            print("暂无邮箱账号")
        else:
            for i, account in enumerate(config["accounts"], 1):
                status = "✅" if account["enabled"] else "❌"
                print(f"{i}. {status} {account['name']} ({account['email']})")

        print("\n操作选项:")
        print("1. 添加邮箱账号")
        print("2. 删除邮箱账号")
        print("3. 启用/禁用账号")
        print("4. 返回主菜单")

        choice = input("\n请选择操作 (1-4): ").strip()

        if choice == '1':
            add_account()
            config = load_config()
        elif choice == '2':
            delete_account()
            config = load_config()
        elif choice == '3':
            toggle_account()
            config = load_config()
        elif choice == '4':
            break
        else:
            print("❌ 无效选择")

def delete_account():
    """删除邮箱账号"""
    config = load_config()

    if not config["accounts"]:
        print("❌ 暂无邮箱账号")
        return

    print("\n🗑️ 删除邮箱账号")
    print("=" * 40)

    for i, account in enumerate(config["accounts"], 1):
        print(f"{i}. {account['name']} ({account['email']})")

    try:
        index = int(input("\n请选择要删除的账号序号: ")) - 1
        if 0 <= index < len(config["accounts"]):
            account = config["accounts"][index]
            confirm = input(f"确认删除账号 {account['name']}？(y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                config["accounts"].pop(index)
                save_config(config)
                print("✅ 账号删除成功")
        else:
            print("❌ 无效序号")
    except ValueError:
        print("❌ 请输入有效数字")

def toggle_account():
    """启用/禁用账号"""
    config = load_config()

    if not config["accounts"]:
        print("❌ 暂无邮箱账号")
        return

    print("\n🔄 启用/禁用账号")
    print("=" * 40)

    for i, account in enumerate(config["accounts"], 1):
        status = "启用" if account["enabled"] else "禁用"
        print(f"{i}. {account['name']} ({account['email']}) - {status}")

    try:
        index = int(input("\n请选择要切换状态的账号序号: ")) - 1
        if 0 <= index < len(config["accounts"]):
            config["accounts"][index]["enabled"] = not config["accounts"][index]["enabled"]
            save_config(config)
            status = "启用" if config["accounts"][index]["enabled"] else "禁用"
            print(f"✅ 账号状态已切换为: {status}")
        else:
            print("❌ 无效序号")
    except ValueError:
        print("❌ 请输入有效数字")

def update_settings():
    """更新全局设置"""
    print("\n⚙️ 全局设置")
    print("=" * 40)

    config = load_config()

    days = input(f"搜索天数范围 (当前{config['search_days']}天): ").strip()
    if days and days.isdigit():
        config["search_days"] = int(days)

    folder = input(f"下载文件夹 (当前{config['download_folder']}): ").strip()
    if folder:
        config["download_folder"] = folder

    threads = input(f"最大线程数 (当前{config['max_threads']}): ").strip()
    if threads and threads.isdigit() and 1 <= int(threads) <= 10:
        config["max_threads"] = int(threads)

    if save_config(config):
        print("✅ 设置保存成功！")
        return True
    else:
        print("❌ 设置保存失败")
        return False

# ==================== 下载功能 ====================

def download_single_account(account, config, thread_lock):
    """下载单个账号的Excel文件"""
    account_name = account["name"]
    account_email = account["email"]
    account_password = account["password"]

    with thread_lock:
        print(f"🔗 [{account_name}] 正在连接邮箱...")

    try:
        mail = imaplib.IMAP4_SSL('imap.qq.com')
        mail.login(account_email, account_password)

        with thread_lock:
            print(f"✅ [{account_name}] 邮箱连接成功")

        mail.select('INBOX')

        from datetime import datetime, timedelta
        since_date = (datetime.now() - timedelta(days=config["search_days"])).strftime("%d-%b-%Y")
        status, messages = mail.search(None, f'SINCE {since_date}')

        if status != 'OK':
            with thread_lock:
                print(f"❌ [{account_name}] 搜索邮件失败")
            return {"account": account_name, "success": False, "count": 0, "error": "搜索邮件失败"}

        email_ids = messages[0].split()
        with thread_lock:
            print(f"📧 [{account_name}] 找到 {len(email_ids)} 封邮件，正在检查Excel附件...")

        downloaded_count = 0

        # 创建账号专用文件夹
        account_folder = os.path.join(config["download_folder"], f"{account_name}_{account_email.split('@')[0]}")
        if not os.path.exists(account_folder):
            os.makedirs(account_folder)

        # 处理每封邮件
        for i, email_id in enumerate(email_ids, 1):
            try:
                status, msg_data = mail.fetch(email_id, '(RFC822)')
                if status != 'OK':
                    continue

                email_message = email.message_from_bytes(msg_data[0][1])

                # 获取邮件信息
                subject = email_message['Subject'] or '无主题'
                if subject:
                    subject = email.header.decode_header(subject)[0][0]
                    if isinstance(subject, bytes):
                        subject = subject.decode('utf-8', errors='ignore')

                sender = email_message['From'] or '未知发件人'
                if sender:
                    sender = email.header.decode_header(sender)[0][0]
                    if isinstance(sender, bytes):
                        sender = sender.decode('utf-8', errors='ignore')

                # 检查附件
                for part in email_message.walk():
                    if part.get_content_disposition() == 'attachment':
                        filename = part.get_filename()
                        if filename:
                            # 解码文件名
                            if not isinstance(filename, str):
                                decoded = email.header.decode_header(filename)[0]
                                filename = decoded[0]
                                if isinstance(filename, bytes):
                                    filename = filename.decode(decoded[1] or 'utf-8', errors='ignore')

                            # 检查是否为Excel文件
                            if filename.lower().endswith(('.xlsx', '.xls', '.xlsm', '.xlsb')):
                                # 创建邮件文件夹
                                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                                email_folder = os.path.join(account_folder, f"邮件_{email_id.decode()}_{timestamp}")
                                if not os.path.exists(email_folder):
                                    os.makedirs(email_folder)

                                # 清理文件名
                                clean_filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
                                filepath = os.path.join(email_folder, clean_filename)

                                # 保存附件
                                with open(filepath, 'wb') as f:
                                    f.write(part.get_payload(decode=True))

                                # 保存邮件信息
                                info_file = os.path.join(email_folder, '邮件信息.txt')
                                with open(info_file, 'w', encoding='utf-8') as f:
                                    f.write(f"账号: {account_name} ({account_email})\n")
                                    f.write(f"主题: {subject}\n")
                                    f.write(f"发件人: {sender}\n")
                                    f.write(f"日期: {email_message['Date']}\n")
                                    f.write(f"附件: {clean_filename}\n")

                                downloaded_count += 1
                                with thread_lock:
                                    print(f"📥 [{account_name}] 下载完成: {clean_filename}")

            except Exception as e:
                with thread_lock:
                    print(f"⚠️  [{account_name}] 处理邮件时出错: {e}")
                continue

        mail.close()
        mail.logout()

        with thread_lock:
            print(f"🎉 [{account_name}] 下载完成！共下载 {downloaded_count} 个Excel文件")

        return {"account": account_name, "success": True, "count": downloaded_count, "error": None}

    except Exception as e:
        with thread_lock:
            print(f"❌ [{account_name}] 错误: {e}")
        return {"account": account_name, "success": False, "count": 0, "error": str(e)}

def download_excel_from_all_accounts():
    """从所有启用的QQ邮箱下载Excel文件"""
    config = load_config()

    # 检查是否有启用的账号
    enabled_accounts = [acc for acc in config["accounts"] if acc["enabled"]]
    if not enabled_accounts:
        print("❌ 没有启用的邮箱账号！")
        print("💡 请先添加并启用邮箱账号")
        return

    # 创建下载文件夹
    download_folder = config["download_folder"]
    if not os.path.exists(download_folder):
        os.makedirs(download_folder)
        print(f"📁 创建下载文件夹: {download_folder}")

    print(f"\n🚀 开始多线程下载，共 {len(enabled_accounts)} 个账号")
    print(f"📊 最大线程数: {config['max_threads']}")
    print(f"📅 搜索范围: 最近{config['search_days']}天")
    print("-" * 50)

    # 创建线程锁
    thread_lock = threading.Lock()

    # 使用线程池执行下载
    with ThreadPoolExecutor(max_workers=config["max_threads"]) as executor:
        # 提交所有下载任务
        future_to_account = {
            executor.submit(download_single_account, account, config, thread_lock): account
            for account in enabled_accounts
        }

        # 收集结果
        results = []
        for future in as_completed(future_to_account):
            account = future_to_account[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                with thread_lock:
                    print(f"❌ [{account['name']}] 线程执行错误: {e}")
                results.append({
                    "account": account['name'],
                    "success": False,
                    "count": 0,
                    "error": f"线程执行错误: {e}"
                })

    # 显示总结
    print("\n" + "=" * 50)
    print("📊 下载结果总结")
    print("=" * 50)

    total_files = 0
    success_count = 0

    for result in results:
        if result["success"]:
            print(f"✅ {result['account']}: 成功下载 {result['count']} 个文件")
            total_files += result['count']
            success_count += 1
        else:
            print(f"❌ {result['account']}: 失败 - {result['error']}")

    print("-" * 50)
    print(f"📈 成功账号: {success_count}/{len(enabled_accounts)}")
    print(f"📥 总下载文件数: {total_files}")
    print(f"📁 文件保存在: {os.path.abspath(download_folder)}")

def show_config():
    """显示当前配置"""
    config = load_config()
    print("\n📋 当前配置信息")
    print("=" * 40)

    if not config["accounts"]:
        print("暂无邮箱账号")
    else:
        print("邮箱账号列表:")
        for i, account in enumerate(config["accounts"], 1):
            status = "✅启用" if account["enabled"] else "❌禁用"
            print(f"  {i}. {status} {account['name']} ({account['email']})")

    print(f"\n全局设置:")
    print(f"  下载文件夹: {config['download_folder']}")
    print(f"  搜索天数: {config['search_days']}天")
    print(f"  最大线程数: {config['max_threads']}")

def show_menu():
    """显示主菜单"""
    print("\n" + "=" * 50)
    print("🔧 QQ邮箱Excel文件下载器 - 多邮箱多线程版")
    print("=" * 50)
    print("1. 管理邮箱账号")
    print("2. 开始下载Excel文件")
    print("3. 全局设置")
    print("4. 查看当前配置")
    print("5. 退出程序")
    print("-" * 50)

def main():
    """主函数"""
    while True:
        show_menu()
        choice = input("请选择操作 (1-5): ").strip()

        if choice == '1':
            manage_accounts()
        elif choice == '2':
            config = load_config()
            enabled_accounts = [acc for acc in config["accounts"] if acc["enabled"]]

            if not enabled_accounts:
                print("❌ 没有启用的邮箱账号！")
                print("💡 请先添加并启用邮箱账号")
                continue

            print(f"\n📧 启用的邮箱账号 ({len(enabled_accounts)}个):")
            for account in enabled_accounts:
                print(f"  • {account['name']} ({account['email']})")

            print(f"\n📅 搜索范围: 最近{config['search_days']}天")
            print(f"📁 下载目录: {config['download_folder']}")
            print(f"🧵 最大线程数: {config['max_threads']}")

            confirm = input("\n确认开始多线程下载？(y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是', '确认']:
                download_excel_from_all_accounts()
            else:
                print("❌ 取消下载")
        elif choice == '3':
            update_settings()
        elif choice == '4':
            show_config()
        elif choice == '5':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请输入1-5")

        input("\n按回车键继续...")

if __name__ == "__main__":
    print("💡 获取QQ邮箱授权码方法：")
    print("   1. 登录QQ邮箱网页版")
    print("   2. 设置 → 账户 → POP3/IMAP/SMTP服务")
    print("   3. 开启IMAP/SMTP服务并生成授权码")
    print("   注意：授权码不是QQ密码！")
    main()
