import datetime
import re
import time

import pandas as pd
import pymysql
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 读取Excel文件, 数据要求，第一列为案件编号，第二列为原始日期，第三列为新日期，没有列标题和大标题
df = pd.read_excel(r'C:\Users\<USER>\Desktop\修改\结案日期.xlsx', engine='openpyxl', header=None)

# 初始化新的列表
newID = []
newTime = []
oldTime = []


def TimeFormatting(dataTime):
    date_obj = datetime.datetime.strptime(dataTime, "%Y年%m月%d日")
    timestamp = date_obj.strftime('%Y-%m-%d')
    return timestamp


def Normal(str, value):
    re_compile = re.compile(str, re.S)
    search = re_compile.search(value)
    group = search.group("date")
    return group


# 匹配项数组
NormalString = [
    r"结案日期：(?P<date>\d+年\d+月\d+日)；",
    r"结案日期(?P<date>\d+年\d+月\d+日)；",
    r"结案日期(?P<date>\d+年\d+月\d+日)",
    r"结案日期(?P<date>\d{4}-\d{2}-\d{2})",
    r"结案日期：(?P<date>\d{4}-\d{2}-\d{2})",
    r"结案日期：(?P<date>\d{4}-\d{2}-\d{2})；",
    r"结案日期<(?P<date>.*?)>",
    r"结案日期(?P<date>\d+年\d+月\d+日)",
    r"结案日期<(?P<date>.*?)>",
    r"结案日期(?P<date>\d+年\d+月\d+日)",
    r"结案日期(?P<date>\d{4}-\d{2}-\d{2})"
]


def searchTime(array, value):
    tempValue = value
    for i in NormalString:
        try:
            # 尝试使用re模块匹配日期字符串，失败会抛出异常
            normal = Normal(i, value)
            # 尝试将中文日期字符转换为时间日期
            tempValue = TimeFormatting(normal)
            break
        except ValueError:
            tempValue = normal
            break
        except AttributeError:
            tempValue = value
    array.append(tempValue)


# 遍历每一行数据
for index, row in df.iterrows():
    id = row[0].replace("\n", "").replace("'", "")
    newID.append(id)

    # 尝试将日期字符串转换为日期对象
    try:
        oldTime.append(TimeFormatting(row[1]))
    except ValueError:
        searchTime(oldTime, row[1])

    try:
        newTime.append(TimeFormatting(row[2]))
    except ValueError:
        searchTime(newTime, row[2])

for i in range(len(newID)):
    logging.info(f"案件编号: {newID[i]},原始日期: {oldTime[i]}, 更改日期: {newTime[i]}")

if input("批量修改结案日期，数据校验是否正确？y/n") == "y":
    logging.info("开始批量修改")
else:
    exit()


# 数据库连接配置
db_config = {
    "host": "************",
    "user": "user_xzcf",
    "password": "Nmgyjj@2022",
    "database": "lcm_project",
    "port": 3306,
    "charset": "utf8"
}


def get_case_data(cursor, state_relation_id, case_no):
    query_sql = """
    SELECT * from operate_suggestion os
    JOIN t_push_case_info_task t ON os.case_source_id = t.case_id and os.state_relation_id = %s
    WHERE t.case_no = %s
    """
    cursor.execute(query_sql, (state_relation_id, case_no))
    return cursor.fetchone()


def update_operate_suggestion(cursor, case_no, state_relation_id, register_time):
    update_sql = """
    update operate_suggestion os
    JOIN t_push_case_info_task t ON os.case_source_id = t.case_id and os.state_relation_id = %s
    SET os.operate_time = %s
    WHERE t.case_no = %s
    """
    cursor.execute(update_sql, (state_relation_id, register_time, case_no))


try:
    with pymysql.connect(**db_config) as connection:
        with connection.cursor() as cursor:
            state_relation_id = "d8aa6db9-1af6-498b-86d3-1c01686dbfd6"
            for i in range(len(newID)):
                case_no = newID[i]
                register_time = newTime[i]

                print(f"案件编号: {newID[i]},原始日期: {oldTime[i]}, 更改日期: {newTime[i]}")
                # 查原数据
                result = get_case_data(cursor, state_relation_id, case_no)
                logging.info(f"{case_no}原数据: {result}")
                time.sleep(1)

                if result:
                    # 更新 operate_suggestion 表
                    update_operate_suggestion(cursor, case_no, state_relation_id, register_time)
                    time.sleep(1)
                    updated_operate_suggestion = get_case_data(cursor, state_relation_id, case_no)
                    logging.info(f"{case_no}更新后的 filling_case 数据: {updated_operate_suggestion}")
                else:
                    logging.warning(f"未找到案件编号: {case_no}")
                time.sleep(1)
                print()

            time.sleep(2)
            if input("人工校验是否通过？y/n") == "y":
                logging.info("提交更改")
                connection.commit()
            else:
                logging.info("取消更改")
                connection.rollback()
except pymysql.MySQLError as e:
    logging.error(f"数据库操作失败: {e}")
