<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="false" level="WARNING" enabled_by_default="false">
      <option name="ignoredPackages">
        <value>
          <list size="3">
            <item index="0" class="java.lang.String" itemvalue="openai" />
            <item index="1" class="java.lang.String" itemvalue="dashscope" />
            <item index="2" class="java.lang.String" itemvalue="Pillow" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>