from flask import Blueprint, request, jsonify
import logging
import requests
import json
import re
from datetime import datetime
import time
import threading
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
import pymysql

# 创建Blueprint
crawler = Blueprint('crawler', __name__)

# 邮件配置
EMAIL_HOST = "smtp.qq.com"  # QQ邮箱SMTP服务器
EMAIL_PORT = 587  # QQ邮箱SMTP端口
EMAIL_HOST_USER = "<EMAIL>"  # 发件人邮箱
EMAIL_HOST_PASSWORD = "dznhnvasiasmghcf"  # QQ邮箱授权码
EMAIL_RECEIVER = ["<EMAIL>"]  # 收件人邮箱列表

# 数据库连接配置
def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(
            host='************',
            user='root',
            password='10001000',
            database='job_crawler',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
    except Exception as e:
        logging.error(f"数据库连接失败: {str(e)}")
        raise

@crawler.route('/bs/crawl', methods=['POST'])
def crawl_data():
    """
    只爬取启用（status=1）的北森URL
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 只获取启用的北森URL
        cursor.execute("SELECT * FROM urls WHERE type = '北森' AND status = 1")
        urls = cursor.fetchall()
        
        if not urls:
            cursor.close()
            conn.close()
            return jsonify({
                'code': 400,
                'message': '未找到有效的北森招聘URL',
                'data': None
            })
        
        total_jobs = 0
        new_jobs = 0
        updated_jobs = 0
        
        for url_item in urls:
            base_url = url_item['url']
            detail_url = url_item['detail_url']
            category = str(url_item['Category'])
            api_url = f"{base_url}/api/Jobad/GetJobAdPageList"
            
            # 定义请求头
            headers = {
                "Content-Type": "application/json"
            }
            
            # 构建请求参数
            request_data = {
                "PageIndex": 0,
                "PageSize": 1000,
                "Category": [category],  # 使用URL表中的Category字段
                "KeyWords": "",
                "SpecialType": 0,
                "PortalId": "",
                "DisplayFields": [
                    "Category",
                    "Kind",
                    "LocId",
                    "PostDate",
                    "WorkWeChatQrCode",
                    "Salary"
                ]
            }
            
            # 转换为JSON
            data_json = json.dumps(request_data, separators=(',', ':'))
            
            # 发送请求
            logging.info(f"爬取URL: {api_url}, Category: {category}")
            
            try:
                response = requests.post(api_url, headers=headers, data=data_json)
            except Exception as e:
                logging.error(f"请求失败: {str(e)}")
                continue
            
            # 检查响应
            if response.status_code == 200:
                try:
                    response_data = response.json()
                except Exception as e:
                    logging.error(f"JSON解析失败: {str(e)}")
                    continue
                
                # 记录响应数据便于调试
                logging.info(f"响应状态码: {response.status_code}")
                logging.info(f"响应数据前100个字符: {str(response_data)[:100]}")
                
                # 检查响应数据是否有效
                if 'Data' in response_data and isinstance(response_data['Data'], list):
                    jobs_data = response_data['Data']
                    total_jobs += len(jobs_data)
                    logging.info(f"发现岗位数量: {len(jobs_data)}")
                    
                    # 每个URL的数据处理完成后，立即提交事务
                    batch_counter = 0
                    
                    # 处理每个岗位信息
                    for job in jobs_data:
                        try:
                            # 提取基本信息
                            job_id = job.get('Id', '')
                            if not job_id:
                                logging.warning("跳过：岗位ID为空")
                                continue
                                
                            job_ad_id = job.get('JobAdId', 0)
                            company_name = job.get('Org', '未知公司')
                            position_name = job.get('JobAdName', '')
                            if not position_name:
                                logging.warning("跳过：岗位名称为空")
                                continue
                            salary = job.get('Salary', '面议')
                            work_location = ','.join(job.get('LocNames', [])) if job.get('LocNames') else ''
                            position_desc = job.get('Duty', '')
                            position_requirement = job.get('Require', '')
                            company_type = ''  # 公司类型空着
                            position_type = job.get('Kind', '')  # Kind对应position_type
                            work_type = job.get('Category', '')
                            post_date = job.get('PostDate', '').replace('T', ' ')
                            end_date = job.get('EndTime', '').replace('T', ' ')
                            education_req = job.get('Degree', '')
                            major_req = ''
                            
                            # 创建唯一的position_link
                            if job_id:
                                position_link = f"{base_url}/{detail_url}?jobAdId={job_id}"
                            else:
                                position_link = f"{base_url}/{detail_url}"
                                
                            backup_link = f"{base_url}/Home/Index"
                            
                            # 详细记录查询条件
                            logging.info(f"检查岗位是否存在 - 职位名称: '{position_name}'")
                            
                            # 通过岗位名称、岗位职责和岗位要求查找记录
                            # 由于职责和要求可能是长文本，使用模糊匹配
                            # 先通过岗位名称筛选
                            cursor.execute(
                                "SELECT * FROM jobs WHERE position_name = %s",
                                (position_name,)
                            )
                            possible_jobs = cursor.fetchall()
                            
                            # 对于找到的每个可能匹配的岗位，进一步比较职责和要求
                            existing_job = None
                            for job_record in possible_jobs:
                                # 使用简单的文本相似度检查（长度和内容）
                                desc_similar = False
                                req_similar = False
                                
                                # 检查职责是否相似
                                if position_desc and job_record['position_desc']:
                                    # 简单检查：如果两个字段的长度相差不超过20%且包含相同的关键部分
                                    desc_len_ratio = len(position_desc) / max(1, len(job_record['position_desc']))
                                    if 0.8 <= desc_len_ratio <= 1.2 and (
                                        position_desc[:50] in job_record['position_desc'] or 
                                        job_record['position_desc'][:50] in position_desc
                                    ):
                                        desc_similar = True
                                elif not position_desc and not job_record['position_desc']:
                                    # 两者都是空的，也视为相似
                                    desc_similar = True
                                
                                # 检查要求是否相似
                                if position_requirement and job_record['position_requirement']:
                                    # 简单检查：如果两个字段的长度相差不超过20%且包含相同的关键部分
                                    req_len_ratio = len(position_requirement) / max(1, len(job_record['position_requirement']))
                                    if 0.8 <= req_len_ratio <= 1.2 and (
                                        position_requirement[:50] in job_record['position_requirement'] or 
                                        job_record['position_requirement'][:50] in position_requirement
                                    ):
                                        req_similar = True
                                elif not position_requirement and not job_record['position_requirement']:
                                    # 两者都是空的，也视为相似
                                    req_similar = True
                                
                                # 如果职责和要求都相似，则认为是同一个岗位
                                if desc_similar and req_similar:
                                    existing_job = job_record
                                    logging.info(f"找到匹配的岗位，ID: {existing_job['id']}")
                                    break
                            
                            if existing_job:
                                # 检查是否有需要更新的字段变化
                                need_update = False
                                changes = []
                                
                                # 检查岗位名称
                                if existing_job['position_name'] != position_name:
                                    changes.append(f"岗位名称: {existing_job['position_name']} -> {position_name}")
                                    need_update = True
                                
                                # 检查岗位类型
                                if existing_job['position_type'] != position_type:
                                    changes.append(f"岗位类型: {existing_job['position_type']} -> {position_type}")
                                    need_update = True
                                
                                # 检查工作地点
                                if existing_job['work_location'] != work_location:
                                    changes.append(f"工作地点: {existing_job['work_location']} -> {work_location}")
                                    need_update = True
                                
                                # 检查薪资
                                if existing_job['salary'] != salary:
                                    changes.append(f"薪资: {existing_job['salary']} -> {salary}")
                                    need_update = True
                                
                                # 检查岗位职责
                                if existing_job['position_desc'] != position_desc:
                                    changes.append("岗位职责有变化")
                                    need_update = True
                                
                                # 检查岗位要求
                                if existing_job['position_requirement'] != position_requirement:
                                    changes.append("岗位要求有变化")
                                    need_update = True
                                
                                if need_update:
                                    # 有字段变化，需要更新
                                    logging.info(f"岗位ID: {existing_job['id']}有以下变化，需要更新: {', '.join(changes)}")
                                    
                                    # 更新现有职位
                                    cursor.execute(
                                        """
                                        UPDATE jobs SET
                                        company_type = %s, work_type = %s, work_location = %s,
                                        education_req = %s, major_req = %s, salary = %s, position_type = %s,
                                        end_date = %s, position_link = %s, backup_link = %s, 
                                        position_desc = %s, position_requirement = %s,
                                        updated_at = CURRENT_TIMESTAMP
                                        WHERE id = %s
                                        """,
                                        (company_type, work_type, work_location,
                                        education_req, major_req, salary, position_type, end_date, position_link, backup_link,
                                        position_desc, position_requirement,
                                        existing_job['id'])
                                    )
                                    updated_jobs += 1
                                    logging.info(f"更新岗位: {position_name}")
                                else:
                                    # 没有变化，不需要更新
                                    logging.info(f"岗位ID: {existing_job['id']}没有变化，跳过更新")
                            else:
                                logging.info("未找到匹配岗位，将新增记录")
                                # 插入新职位
                                cursor.execute(
                                    """
                                    INSERT INTO jobs 
                                    (company_name, company_type, work_type, position_name, 
                                    work_location, education_req, major_req, salary, position_type, post_date, end_date, 
                                    position_link, backup_link, position_desc, position_requirement)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                    """,
                                    (company_name, company_type, work_type, position_name,
                                    work_location, education_req, major_req, salary, position_type, post_date, end_date,
                                    position_link, backup_link, position_desc, position_requirement)
                                )
                                new_jobs += 1
                                logging.info(f"新增岗位: {position_name}")
                            
                            # 每处理10条数据，提交一次事务
                            batch_counter += 1
                            if batch_counter >= 10:
                                conn.commit()
                                logging.info(f"批量提交10条数据")
                                batch_counter = 0
                                
                        except Exception as e:
                            logging.error(f"处理岗位数据时出错: {str(e)}")
                            continue
                    
                    # 每个URL的数据处理完成后，提交剩余的事务
                    if batch_counter > 0:
                        conn.commit()
                        logging.info(f"提交剩余{batch_counter}条数据")
                else:
                    logging.warning(f"响应数据格式不正确: {str(response_data)[:200]}")
            else:
                logging.error(f"请求失败，状态码: {response.status_code}, 响应: {response.text[:200]}")
                
        # 关闭数据库连接
        cursor.close()
        conn.close()
        
        return jsonify({
            'code': 200,
            'message': '爬取成功',
            'data': {
                'total_jobs': total_jobs,
                'new_jobs': new_jobs,
                'updated_jobs': updated_jobs
            }
        })
        
    except Exception as e:
        logging.error(f"爬取数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'爬取数据失败: {str(e)}',
            'data': None
        })

@crawler.route('/bs/zl_crawl', methods=['POST'])
def zl_crawl():
    """
    只爬取启用（status=1）的智联URL
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM urls WHERE type = '智联' AND status = 1")
        urls = cursor.fetchall()
        
        if not urls:
            return jsonify({
                'code': 400,
                'message': '未找到有效的智联招聘URL',
                'data': None
            })
        
        total_jobs = 0
        new_jobs = 0
        
        for url_item in urls:
            base_url = url_item['url']
            logging.info(f"处理智联招聘URL: {base_url}")
            
            # 从URL中提取company_id和position_source_type参数
            company_id_match = re.search(r'company/([^?/]+)', base_url)
            job_source_type_match = re.search(r'jobSourceType=(\d+)', base_url)
            
            if not company_id_match:
                logging.error(f"无法从URL中提取company_id: {base_url}")
                continue
                
            company_id = company_id_match.group(1)
            job_source_type = int(job_source_type_match.group(1)) if job_source_type_match else 2  # 默认值为2
            
            logging.info(f"提取参数 - company_id: {company_id}, job_source_type: {job_source_type}")
            
            headers = {
                "accept": "application/json, text/plain, */*",
                "accept-language": "en-GB,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,en-US;q=0.6",
                "cache-control": "no-cache",
                "content-type": "application/json",
                "origin": "https://xiaoyuan.zhaopin.com",
                "pragma": "no-cache",
                "priority": "u=1, i",
                "referer": "https://xiaoyuan.zhaopin.com/",
                "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
                "x-zp-at;": "",
                "x-zp-business-system": "40",
                "x-zp-platform": "14",
                "x-zp-rt;": ""
            }

            list_api_url = "https://cgate.zhaopin.com/positionbusiness/searchrecommend/searchPositionsCompany"
            params = {
                "x-zp-page-request-id": "1",
                "x-zp-client-id": "1"
            }
            
            list_request_data = {
                "S_SOU_POSITION_SOURCE_TYPE": job_source_type,
                "S_SOU_COMPANY_ID": company_id,
                "eventScenario": "PCCampusSearchPositionsCompany",
                "identity": "1",
                "pageIndex": 1,
                "pageSize": 20,
                "S_SOU_FULL_INDEX": "",
                "at": "",
                "rt": "",
                "channel": "xiaoyuan",
                "platform": "14",
                "version": "0.0.0",
                "d": "261e6f79-ffe1-4442-b7dc-52029b454840"
            }
            
            list_data_json = json.dumps(list_request_data, separators=(',', ':'))
            logging.info(f"发送列表请求到: {list_api_url}")
            list_response = requests.post(list_api_url, headers=headers, params=params, data=list_data_json)

            if list_response.status_code == 200:
                list_response_data = list_response.json()
                
                logging.info(f"列表响应状态码: {list_response.status_code}")
                logging.info(f"列表响应数据前100个字符: {str(list_response_data)[:100]}")
                
                # 检查列表响应数据是否有效
                if 'data' in list_response_data and 'list' in list_response_data['data'] and isinstance(list_response_data['data']['list'], list):
                    jobs_list = list_response_data['data']['list']
                    total_jobs += len(jobs_list)
                    logging.info(f"发现岗位数量: {len(jobs_list)}")
                    
                    cookies = {
                        "campusOperateJobUserInfo": "63801def-b22b-468b-8aba-72a573615749",
                        "x-zp-client-id": "261e6f79-ffe1-4442-b7dc-52029b454841",
                        "sajssdk_2015_cross_new_user": "1",
                        "locationInfo_job": "{%22code%22:%22740%22%2C%22name%22:%22%E8%A5%84%E9%98%B3%22}",
                        "sensorsdata2015jssdkcross": "%7B%22distinct_id%22%3A%22196714841407b-029b6d1278a91f2-4c657b58-2073600-196714841411866%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk2NzE0ODQxNDA3Yi0wMjliNmQxMjc4YTkxZjItNGM2NTdiNTgtMjA3MzYwMC0xOTY3MTQ4NDE0MTE4NjYifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%22196714841407b-029b6d1278a91f2-4c657b58-2073600-196714841411866%22%7D"
                    }
                    
                    detail_api_url = "https://cgate.zhaopin.com/positionbusiness/exposure/getPositionDetail"
                    detail_params = {
                        "x-zp-page-request-id": "3af430787cf74781a5245a72380b6cec-1745674941157-985633",
                        "x-zp-client-id": "261e6f79-ffe1-4442-b7dc-52029b454841"
                    }
                    
                    for job in jobs_list:
                        try:
                            job_number = job.get('number', '')
                            if not job_number:
                                logging.error(f"无法获取岗位编号: {str(job)[:100]}")
                                continue
                                
                            company_name = job.get('companyName', '未知公司')
                            position_name = job.get('name', '')
                            
                            # 设置详情请求数据
                            detail_request_data = {
                                "identity": "1",
                                "number": job_number,
                                "at": "",
                                "rt": "",
                                "channel": "xiaoyuan",
                                "platform": "14",
                                "version": "0.0.0",
                                "d": "261e6f79-ffe1-4442-b7dc-52029b454841"
                            }
                            
                            # 发送详情请求
                            detail_data_json = json.dumps(detail_request_data, separators=(',', ':'))
                            logging.info(f"发送详情请求到: {detail_api_url}, 岗位编号: {job_number}")
                            detail_response = requests.post(detail_api_url, headers=headers, cookies=cookies, params=detail_params, data=detail_data_json)
                            

                            if detail_response.status_code == 200:
                                detail_response_data = detail_response.json()
                                if 'data' in detail_response_data and 'positionDetail' in detail_response_data['data']:
                                    position_detail = detail_response_data['data']['positionDetail']
                                    company_detail = detail_response_data['data'].get('companyDetail', {})
                                    

                                    company_type = company_detail.get('property', '')
                                    job_type = position_detail.get('jobTypeLevelName', '')
                                    work_location = position_detail.get('workAddress', '') or position_detail.get('positionWorkCity', '')
                                    education_req = position_detail.get('education', '')
                                    major_req = ', '.join(position_detail.get('needMajor', [])) if 'needMajor' in position_detail and position_detail['needMajor'] else ''
                                    salary = position_detail.get('salary60', '面议')
                                    position_type = position_detail.get('workType', '')
                                    work_type = "校招网申" 
                                    post_date = position_detail.get('positionPublishTime', '').replace('T', ' ')
                                    end_date = position_detail.get('dateEnd', '').replace('T', ' ')
                                    position_link = position_detail.get('positionURL', '') or f"https://xiaoyuan.zhaopin.com/position/{job_number}"
                                    backup_link = base_url

                                    job_desc = position_detail.get('jobDesc', '')
                                    position_desc = ''
                                    position_requirement = ''

                                    if job_desc:
   
                                        desc_start_1 = job_desc.find("岗位描述")
                                        desc_start_2 = job_desc.find("岗位职责")
                                        desc_start = max(desc_start_1, desc_start_2) if desc_start_1 != -1 and desc_start_2 != -1 else (desc_start_1 if desc_start_1 != -1 else desc_start_2)
                                        welfare_start = job_desc.find("福利待遇")
                                        
                                        req_start = job_desc.find("任职要求")
                                        
                                        if desc_start != -1 and welfare_start != -1 and desc_start < welfare_start:
                                            position_desc = job_desc[desc_start:welfare_start].strip()
                                        elif desc_start != -1:
                                            if req_start != -1 and desc_start < req_start:
                                                position_desc = job_desc[desc_start:req_start].strip()
                                            else:
                                                position_desc = job_desc[desc_start:].strip()
                                        
                                        if req_start != -1:
                                            position_requirement = job_desc[req_start:].strip()
                                        
                                        if not position_desc and not position_requirement:
                                            position_desc = job_desc
                                            
                                        logging.info(f"分割后的岗位职责: {position_desc[:50]}...")
                                        logging.info(f"分割后的岗位要求: {position_requirement[:50]}...")
                                    
                                    # 构建唯一标识，使用position_name, company_name和post_date组合
                                    cursor.execute(
                                        "SELECT * FROM jobs WHERE position_name = %s AND company_name = %s AND post_date = %s",
                                        (position_name, company_name, post_date)
                                    )
                                    existing_job = cursor.fetchone()
                                    
                                    if not existing_job:
                                        cursor.execute(
                                            """
                                            INSERT INTO jobs 
                                            (company_name, company_type, work_type, job_type, position_name, 
                                            work_location, education_req, major_req, salary, position_type, post_date, end_date,
                                            position_link, backup_link, position_desc, position_requirement)
                                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                            """,
                                            (company_name, company_type, work_type, job_type, position_name,
                                            work_location, education_req, major_req, salary, position_type, post_date, end_date,
                                            position_link, backup_link, position_desc, position_requirement)
                                        )
                                        new_jobs += 1
                                        logging.info(f"新增岗位: {position_name}")
                                    else:
                                        # 更新现有职位
                                        cursor.execute(
                                            """
                                            UPDATE jobs SET
                                            company_type = %s, work_type = %s, work_location = %s,
                                            education_req = %s, major_req = %s, salary = %s, position_type = %s,
                                            end_date = %s, position_link = %s, backup_link = %s, 
                                            position_desc = %s, position_requirement = %s,
                                            updated_at = CURRENT_TIMESTAMP
                                            WHERE position_name = %s AND company_name = %s AND post_date = %s
                                            """,
                                            (company_type, work_type, work_location,
                                            education_req, major_req, salary, position_type, end_date, position_link, backup_link,
                                            position_desc, position_requirement,
                                            position_name, company_name, post_date)
                                        )
                                        logging.info(f"更新岗位: {position_name}")
                                else:
                                    logging.warning(f"详情响应数据格式不正确: {str(detail_response_data)[:200]}")
                            else:
                                logging.error(f"详情请求失败，状态码: {detail_response.status_code}, 响应: {detail_response.text[:200]}")
                        except Exception as e:
                            logging.error(f"处理岗位数据时出错: {str(e)}")
                            continue
                else:
                    logging.warning(f"列表响应数据格式不正确: {str(list_response_data)[:200]}")
            else:
                logging.error(f"列表请求失败，状态码: {list_response.status_code}, 响应: {list_response.text[:200]}")
                
        # 提交所有更改
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({
            'code': 200,
            'message': '智联招聘爬取成功',
            'data': {
                'total_jobs': total_jobs,
                'new_jobs': new_jobs
            }
        })
        
    except Exception as e:
        logging.error(f"爬取智联招聘数据失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'爬取智联招聘数据失败: {str(e)}',
            'data': None
        })

def get_llm_completion(jobs_data):
    """
    调用大模型进行字段补全，一次处理多条记录
    """
    try:
        # 准备发送给大模型的数据
        jobs_info = []
        for i, job in enumerate(jobs_data):
            job_info = f"""
【岗位{i+1}】
ID: {job['id']}
公司名称: {job.get('company_name', '未知')}
公司类型: {job.get('company_type', '未知')}
岗位名称: {job.get('position_name', '未知')}
工作地点: {job.get('work_location', '未知')}
岗位描述: {job.get('position_desc', '未知')}
岗位要求: {job.get('position_requirement', '未知')}
"""
            jobs_info.append(job_info)
        
        all_jobs_info = "\n".join(jobs_info)
        
        prompt = f"""
请分析以下多个招聘岗位信息，并补全缺失的学历要求、专业要求和岗位类型字段。
只需要返回JSON格式的补全结果，不要有任何其他解释。

{all_jobs_info}

请只返回以下JSON格式数组，每个对象对应一个岗位：
[
  {{
    "id": 岗位ID,
    "education_req": "补全的学历要求",
    "major_req": "补全的专业要求",
    "job_type": "补全的岗位类型"
  }},
  ... 其他岗位
]
"""
        
        api_key = "sk-264af46faf944a31aecc1e1c3ebcc0d1"
        base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        
        import requests
        import json
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        payload = {
            "model": "qwen-plus",
            "messages": [
                {"role": "system", "content": "你是一个专业的HR助手，根据招聘信息补全缺失的字段。请直接返回JSON格式，不要包含任何解释或额外信息。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        try:
            response = requests.post(
                f"{base_url}/chat/completions", 
                headers=headers,
                json=payload,
                timeout=30  # 30秒超时
            )
            
            # 检查响应状态
            if response.status_code != 200:
                logging.error(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
                return []

            response_data = response.json()
            if 'choices' not in response_data or len(response_data['choices']) == 0:
                logging.error(f"API返回的响应中没有有效的 choices: {response_data}")
                return []
            
            content = response_data['choices'][0]['message']['content']
            logging.info(f"大模型原始回复: {content[:200]}...")  # 只记录前200个字符以避免日志过大

            try:
                parsed_data = json.loads(content)
                return parsed_data
            except json.JSONDecodeError:
                # 尝试从文本中提取JSON
                import re
                json_match = re.search(r'(\[[\s\S]*\])', content)
                if json_match:
                    try:
                        parsed_data = json.loads(json_match.group(1))
                        return parsed_data
                    except:
                        logging.error(f"无法解析提取的JSON: {json_match.group(1)[:100]}...")
                
                logging.error(f"响应不是有效的JSON格式: {content[:100]}...")
                return []
                
        except requests.exceptions.Timeout:
            logging.error("API请求超时")
            return []
        except requests.exceptions.RequestException as e:
            logging.error(f"API请求异常: {str(e)}")
            return []
        except Exception as e:
            logging.error(f"处理API响应时出错: {str(e)}")
            return []
            
    except Exception as e:
        logging.error(f"准备请求数据时出错: {str(e)}")
        return []

def auto_complete_missing_fields():
    """
    自动补全缺失字段的主函数
    查询数据库中学历要求为空的和专业要求为空的和岗位类型为空的数据
    循环分页处理所有需要补全的记录
    """
    try:
        logging.info(f"开始执行自动补全任务: {datetime.now()}")
        
        # 建立数据库连接
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询需要补全的记录总数
        cursor.execute("""
            SELECT COUNT(*) as total FROM jobs 
            WHERE (education_req IS NULL OR education_req = '') 
            OR (major_req IS NULL OR major_req = '') 
            OR (job_type IS NULL OR job_type = '')
        """)
        
        total_count = cursor.fetchone()['total']
        
        if total_count == 0:
            logging.info("没有找到需要补全的记录")
            cursor.close()
            conn.close()
            return
        
        # 设置每页大小和处理延迟
        page_size = 3  # 减小每页处理的数量为3条，降低压力
        retry_delay = 15  # 失败后的重试延迟（秒）增加到15秒
        max_retries = 3  # 最大重试次数
        
        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size
        
        logging.info(f"找到总共{total_count}条需要补全的记录，将分{total_pages}页处理")
        
        total_updated = 0
        
        # 循环处理每一页
        for page in range(1, total_pages + 1):
            retry_count = 0
            while retry_count < max_retries:
                try:
                    logging.info(f"开始处理第{page}/{total_pages}页")
                    
                    # 计算偏移量
                    offset = (page - 1) * page_size
                    
                    # 查询当前页的记录
                    cursor.execute("""
                        SELECT * FROM jobs 
                        WHERE (education_req IS NULL OR education_req = '') 
                        OR (major_req IS NULL OR major_req = '') 
                        OR (job_type IS NULL OR job_type = '')
                        LIMIT %s OFFSET %s
                    """, (page_size, offset))
                    
                    jobs = cursor.fetchall()
                    
                    if not jobs:
                        logging.warning(f"第{page}页没有找到记录，跳过")
                        break
                    
                    logging.info(f"第{page}页找到{len(jobs)}条记录")
                    
                    # 调用大模型获取补全结果
                    completion_results = get_llm_completion(jobs)
                    
                    if not completion_results:
                        logging.warning(f"第{page}页未获得有效的补全结果")
                        retry_count += 1
                        if retry_count < max_retries:
                            logging.info(f"等待{retry_delay}秒后重试...")
                            time.sleep(retry_delay)
                            continue
                        else:
                            logging.error(f"第{page}页处理失败，达到最大重试次数，跳过此页")
                        break
                    
                    # 批量更新数据库
                    updates_count = 0
                    
                    for result in completion_results:
                        job_id = result.get('id')
                        if not job_id:
                            logging.warning(f"补全结果缺少ID字段")
                            continue
                        
                        education_req = result.get('education_req', '')
                        major_req = result.get('major_req', '')
                        job_type = result.get('job_type', '')
                        
                        # 检查字段是否有效
                        if not education_req and not major_req and not job_type:
                            logging.warning(f"岗位ID: {job_id} 的补全结果没有有效字段")
                            continue
                        updates = []
                        params = []
                        
                        if education_req:
                            updates.append("education_req = %s")
                            params.append(education_req)
                        
                        if major_req:
                            updates.append("major_req = %s")
                            params.append(major_req)
                        
                        if job_type:
                            updates.append("job_type = %s")
                            params.append(job_type)
                        
                        if updates:
                            updates.append("updated_at = CURRENT_TIMESTAMP")
                            params.append(job_id)
                            
                            update_sql = f"UPDATE jobs SET {', '.join(updates)} WHERE id = %s"
                            cursor.execute(update_sql, params)
                            updates_count += 1
                    
                    conn.commit()
                    logging.info(f"第{page}页成功更新{updates_count}条记录")
                    total_updated += updates_count
                    
                    break
                    
                except Exception as e:
                    logging.error(f"处理第{page}页时出错: {str(e)}")
                    retry_count += 1
                    if retry_count < max_retries:
                        logging.info(f"等待{retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                    else:
                        logging.error(f"第{page}页处理失败，达到最大重试次数")
            if page < total_pages:
                wait_time = 10  
                logging.info(f"等待{wait_time}秒后处理下一页...")
                time.sleep(wait_time)
        
        logging.info(f"自动补全任务完成，总共更新{total_updated}条记录")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logging.error(f"自动补全任务出错: {str(e)}")
        try:
            if 'cursor' in locals() and cursor:
                cursor.close()
            if 'conn' in locals() and conn:
                conn.close()
        except:
            pass

def send_job_notification(new_jobs):
    """
    发送新工作岗位通知邮件
    :param new_jobs: 新增的工作岗位列表
    """
    try:
        msg = MIMEMultipart()
        msg['From'] = Header(EMAIL_HOST_USER)
        msg['To'] = Header(','.join(EMAIL_RECEIVER))
        current_date = datetime.now().strftime('%Y年%m月%d日')
        msg['Subject'] = Header(f'{current_date}当日新增岗位通知')
        
        html_content = f"""
        <html>
        <body>
        <h2>{current_date}新增岗位汇总</h2>
        <p>今日共新增{len(new_jobs)}个岗位</p>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <tr style="background-color: #f2f2f2;">
                <th style="padding: 8px;">公司名称</th>
                <th style="padding: 8px;">岗位名称</th>
                <th style="padding: 8px;">工作地点</th>
                <th style="padding: 8px;">薪资</th>
                <th style="padding: 8px;">发布时间</th>
                <th style="padding: 8px;">链接</th>
            </tr>
        """
        
        for job in new_jobs:
            created_time = job.get('created_at')
            if isinstance(created_time, datetime):
                created_time = created_time.strftime('%H:%M:%S')
            html_content += f"""
            <tr>
                <td style="padding: 8px;">{job.get('company_name', '未知')}</td>
                <td style="padding: 8px;">{job.get('position_name', '未知')}</td>
                <td style="padding: 8px;">{job.get('work_location', '未知')}</td>
                <td style="padding: 8px;">{job.get('salary', '未知')}</td>
                <td style="padding: 8px;">{created_time}</td>
                <td style="padding: 8px;"><a href="{job.get('position_link', '#')}">查看详情</a></td>
            </tr>
            """
        
        html_content += """
        </table>
        <p style="color: #666; font-size: 12px; margin-top: 20px;">
            注：此邮件为自动发送，请勿直接回复。如有问题请联系管理员。
        </p>
        </body>
        </html>
        """
        
        msg.attach(MIMEText(html_content, 'html', 'utf-8'))
        
        with smtplib.SMTP(EMAIL_HOST, EMAIL_PORT) as server:
            server.starttls()
            server.login(EMAIL_HOST_USER, EMAIL_HOST_PASSWORD)
            server.sendmail(EMAIL_HOST_USER, EMAIL_RECEIVER, msg.as_string())
            
        logging.info(f"成功发送当日新增岗位通知邮件，共{len(new_jobs)}个岗位")
    except Exception as e:
        logging.error(f"发送邮件通知失败: {str(e)}")

def scheduled_task():
    """
    定时执行任务的线程函数
    每小时先执行爬虫任务，全部完成后再执行补全任务
    """
    # 为避免循环导入，在函数内部导入app
    from flask import current_app
    from werkzeug.test import Client
    from werkzeug.wrappers import Response
    
    while True:
        try:
            logging.info("开始执行定时任务...")
            
            # 先执行爬虫任务
            crawl_success = True
            
            # 1. 调用北森招聘爬虫
            try:
                logging.info("调用北森招聘爬虫...")
                import requests
                response = requests.post('http://localhost:5000/bs/crawl')
                if response.status_code == 200:
                    response_data = response.json()
                    if response_data.get('code') == 200:
                        logging.info(f"北森招聘爬虫完成，状态: {response_data.get('message', '未知')}")
                    else:
                        logging.warning(f"北森招聘爬虫未成功完成，状态码: {response_data.get('code')}, 消息: {response_data.get('message', '未知')}")
                        crawl_success = False
                else:
                    logging.error(f"请求北森招聘爬虫失败，HTTP状态码: {response.status_code}")
                    crawl_success = False
            except Exception as e:
                logging.error(f"调用北森招聘爬虫出错: {str(e)}")
                crawl_success = False
            
            # 2. 调用智联招聘爬虫
            try:
                logging.info("调用智联招聘爬虫...")
                response = requests.post('http://localhost:5000/bs/zl_crawl')
                if response.status_code == 200:
                    response_data = response.json()
                    if response_data.get('code') == 200:
                        logging.info(f"智联招聘爬虫完成，状态: {response_data.get('message', '未知')}")
                    else:
                        logging.warning(f"智联招聘爬虫未成功完成，状态码: {response_data.get('code')}, 消息: {response_data.get('message', '未知')}")
                        crawl_success = False
                else:
                    logging.error(f"请求智联招聘爬虫失败，HTTP状态码: {response.status_code}")
                    crawl_success = False
            except Exception as e:
                logging.error(f"调用智联招聘爬虫出错: {str(e)}")
                crawl_success = False
            
            # 获取当日新增的岗位
            try:
                conn = get_db_connection()
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM jobs 
                    WHERE DATE(created_at) = CURDATE()
                    ORDER BY created_at DESC
                """)
                new_jobs_list = cursor.fetchall()
                cursor.close()
                conn.close()
                
                # 如果有新增岗位，发送邮件通知
                if new_jobs_list:
                    logging.info(f"发现今日新增{len(new_jobs_list)}个岗位，准备发送邮件通知")
                    send_job_notification(new_jobs_list)
                else:
                    logging.info("今日暂无新增岗位")
            except Exception as e:
                logging.error(f"获取新增岗位失败: {str(e)}")
            
            # 3. 只有爬虫任务成功完成后，才执行补全任务
            if crawl_success:
                try:
                    logging.info("爬虫任务完成，开始调用自动补全任务...")
                    auto_complete_missing_fields()
                    logging.info("自动补全任务完成")
                except Exception as e:
                    logging.error(f"自动补全任务出错: {str(e)}")
            else:
                logging.warning("由于爬虫任务未成功完成，跳过自动补全任务")
            
            logging.info("定时任务执行完成，等待下一次执行...")

            time.sleep(3600)
        except Exception as e:
            logging.error(f"定时任务执行出错: {str(e)}")
            time.sleep(300) 