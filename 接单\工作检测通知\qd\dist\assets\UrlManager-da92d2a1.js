import{_ as S,u as j,r as m,a as q,o as N,b as i,d as u,e as l,F as O,j as T,k as _,t as n,h as k,w as U,v as C,f as R,i as g}from"./index-f0826788.js";import{u as y}from"./request-7c49b76d.js";const z={name:"UrlManager",setup(){const L=j(),t=m(!0),v=m([]),e=m(!1),f=m(!1),b=m(!1),s=m(!1),r=m(null),p=m(!1),a=q({id:null,url:"",type:"",status:"启用",Category:"",detail_url:""}),D=async()=>{t.value=!0;try{const o=await y.getUrls();v.value=(o.data||[]).map(d=>({...d,status:d.status===1?"启用":"禁用"}))}catch(o){console.error("获取URL列表失败",o),alert("获取URL列表失败："+(o.message||"未知错误"))}finally{t.value=!1}},F=()=>{L.push("/")},w=()=>{f.value=!1,Object.keys(a).forEach(o=>{a[o]=o==="id"?null:o==="status"?"启用":""}),e.value=!0},x=o=>{f.value=!0,Object.keys(a).forEach(d=>{d==="status"?a.status=o.status===1||o.status==="启用"?"启用":"禁用":a[d]=o[d]!==void 0?o[d]:d==="status"?"启用":""}),e.value=!0},h=()=>{b.value||(e.value=!1)},V=async()=>{if(!a.url.trim()){alert("请输入URL");return}if(a.type!=="智联"&&!a.Category){alert("请输入分类编号");return}a.type==="智联"&&(a.Category="",a.detail_url="");const o=a.status==="启用"?1:0,d={...a,status:o};b.value=!0;try{let c;if(f.value){c=await y.updateUrl(a.id,d);const M=v.value.findIndex(I=>I.id===a.id);M!==-1&&(v.value[M]={...c.data,status:c.data.status===1?"启用":"禁用"}),alert("URL更新成功")}else c=await y.addUrl(d),v.value.unshift({...c.data,status:c.data.status===1?"启用":"禁用"}),alert("URL添加成功");e.value=!1}catch(c){console.error("保存URL失败",c),alert("保存失败："+(c.message||"未知错误"))}finally{b.value=!1}},E=o=>{r.value=o,s.value=!0},A=()=>{s.value=!1,r.value=null},B=async()=>{if(!(!r.value||!r.value.id)){p.value=!0;try{await y.deleteUrl(r.value.id),v.value=v.value.filter(o=>o.id!==r.value.id),alert("URL删除成功"),s.value=!1,r.value=null}catch(o){console.error("删除URL失败",o),alert("删除失败："+(o.message||"未知错误"))}finally{p.value=!1}}};return N(()=>{D()}),{loading:t,urlList:v,showModal:e,isEditing:f,isSaving:b,urlForm:a,showDeleteConfirm:s,deleteUrl:r,isDeleting:p,goBack:F,openAddModal:w,openEditModal:x,closeModal:h,saveUrl:V,confirmDelete:E,cancelDelete:A,deleteUrlItem:B}}},G={class:"url-manager-container"},H={class:"page-header"},J={class:"content-panel"},K={class:"panel-header"},P={class:"table-wrapper"},Q={key:0,class:"data-table"},W={class:"url-cell"},X={class:"url-cell"},Y={class:"action-buttons"},Z=["onClick"],$=["onClick"],ll={key:1,class:"loading-placeholder"},tl={key:2,class:"empty-placeholder"},el={class:"url-modal-content"},sl={class:"url-modal-header"},ol={class:"url-modal-body"},al={class:"form-group"},nl={class:"form-group"},dl={class:"form-group"},rl={key:0,class:"form-group"},il={key:1,class:"form-group"},ul={class:"form-actions"},cl=["disabled"],vl={class:"confirm-modal-content"},ml={class:"confirm-modal-body"},fl={class:"url-preview"},bl={class:"confirm-actions"},Ul=["disabled"];function gl(L,t,v,e,f,b){return i(),u("div",G,[l("header",H,[t[15]||(t[15]=l("h1",null,"URL管理系统",-1)),t[16]||(t[16]=l("p",{class:"sub-title"},"管理岗位爬取链接",-1)),l("button",{class:"back-btn",onClick:t[0]||(t[0]=(...s)=>e.goBack&&e.goBack(...s))},"返回岗位列表")]),l("section",J,[l("div",K,[t[17]||(t[17]=l("h2",null,"URL列表",-1)),l("button",{class:"add-btn",onClick:t[1]||(t[1]=(...s)=>e.openAddModal&&e.openAddModal(...s))},"新增URL")]),l("div",P,[e.urlList.length>0?(i(),u("table",Q,[t[18]||(t[18]=l("thead",null,[l("tr",null,[l("th",null,"ID"),l("th",null,"URL"),l("th",null,"类型"),l("th",null,"状态"),l("th",null,"请求参数类型"),l("th",null,"详情地址"),l("th",null,"创建时间"),l("th",null,"更新时间"),l("th",null,"操作")])],-1)),l("tbody",null,[(i(!0),u(O,null,T(e.urlList,s=>(i(),u("tr",{key:s.id},[l("td",null,n(s.id),1),l("td",W,n(s.url),1),l("td",null,n(s.type||"-"),1),l("td",null,n(s.status||"-"),1),l("td",null,n(s.Category),1),l("td",X,n(s.detail_url||"-"),1),l("td",null,n(s.created_at),1),l("td",null,n(s.updated_at),1),l("td",null,[l("div",Y,[l("button",{class:"edit-btn",onClick:r=>e.openEditModal(s)},"编辑",8,Z),l("button",{class:"delete-btn",onClick:r=>e.confirmDelete(s)},"删除",8,$)])])]))),128))])])):e.loading?(i(),u("div",ll,t[19]||(t[19]=[l("div",{class:"spinner"},null,-1),l("p",null,"加载中...",-1)]))):(i(),u("div",tl,[t[20]||(t[20]=l("p",null,"暂无URL数据",-1)),l("button",{class:"add-btn",onClick:t[2]||(t[2]=(...s)=>e.openAddModal&&e.openAddModal(...s))},"添加第一个URL")]))])]),e.showModal?(i(),u("div",{key:0,class:"url-modal",onClick:t[11]||(t[11]=_((...s)=>e.closeModal&&e.closeModal(...s),["self"]))},[l("div",el,[l("button",{class:"close-btn",onClick:t[3]||(t[3]=(...s)=>e.closeModal&&e.closeModal(...s))},"×"),l("div",sl,[l("h3",null,n(e.isEditing?"编辑URL":"新增URL"),1)]),l("div",ol,[l("div",al,[t[21]||(t[21]=l("label",null,[k("URL "),l("span",{class:"required"},"*")],-1)),U(l("input",{type:"text","onUpdate:modelValue":t[4]||(t[4]=s=>e.urlForm.url=s),placeholder:"请输入URL"},null,512),[[C,e.urlForm.url]]),t[22]||(t[22]=l("div",{class:"help-text"},"例如: https://example.com",-1))]),l("div",nl,[t[24]||(t[24]=l("label",null,"类型",-1)),U(l("select",{"onUpdate:modelValue":t[5]||(t[5]=s=>e.urlForm.type=s),class:"select-input"},t[23]||(t[23]=[l("option",{value:"北森"},"北森",-1),l("option",{value:"智联"},"智联",-1)]),512),[[R,e.urlForm.type]])]),l("div",dl,[t[26]||(t[26]=l("label",null,[k("状态 "),l("span",{class:"required"},"*")],-1)),U(l("select",{"onUpdate:modelValue":t[6]||(t[6]=s=>e.urlForm.status=s),class:"select-input"},t[25]||(t[25]=[l("option",{value:"启用"},"启用",-1),l("option",{value:"禁用"},"禁用",-1)]),512),[[R,e.urlForm.status]])]),e.urlForm.type!=="智联"?(i(),u("div",rl,[t[27]||(t[27]=l("label",null,[k("请求参数类型 "),l("span",{class:"required"},"*")],-1)),U(l("input",{type:"number","onUpdate:modelValue":t[7]||(t[7]=s=>e.urlForm.Category=s),placeholder:"请输入分类编号"},null,512),[[C,e.urlForm.Category]])])):g("",!0),e.urlForm.type!=="智联"?(i(),u("div",il,[t[28]||(t[28]=l("label",null,"详情地址",-1)),U(l("input",{type:"text","onUpdate:modelValue":t[8]||(t[8]=s=>e.urlForm.detail_url=s),placeholder:"请输入详情地址"},null,512),[[C,e.urlForm.detail_url]])])):g("",!0),l("div",ul,[l("button",{class:"cancel-btn",onClick:t[9]||(t[9]=(...s)=>e.closeModal&&e.closeModal(...s))},"取消"),l("button",{class:"save-btn",onClick:t[10]||(t[10]=(...s)=>e.saveUrl&&e.saveUrl(...s)),disabled:e.isSaving},n(e.isSaving?"保存中...":"保存"),9,cl)])])])])):g("",!0),e.showDeleteConfirm?(i(),u("div",{key:1,class:"confirm-modal",onClick:t[14]||(t[14]=_((...s)=>e.cancelDelete&&e.cancelDelete(...s),["self"]))},[l("div",vl,[t[30]||(t[30]=l("div",{class:"confirm-modal-header"},[l("h3",null,"确认删除")],-1)),l("div",ml,[t[29]||(t[29]=l("p",{class:"confirm-message"},"确定要删除以下URL吗？此操作不可撤销。",-1)),l("div",fl,n(e.deleteUrl?e.deleteUrl.url:""),1)]),l("div",bl,[l("button",{class:"cancel-btn",onClick:t[12]||(t[12]=(...s)=>e.cancelDelete&&e.cancelDelete(...s))},"取消"),l("button",{class:"delete-btn",onClick:t[13]||(t[13]=(...s)=>e.deleteUrlItem&&e.deleteUrlItem(...s)),disabled:e.isDeleting},n(e.isDeleting?"删除中...":"确认删除"),9,Ul)])])])):g("",!0)])}const kl=S(z,[["render",gl],["__scopeId","data-v-57605909"]]);export{kl as default};
