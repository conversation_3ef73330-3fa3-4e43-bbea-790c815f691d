import pandas as pd
import pymysql
import concurrent.futures

# 加载Excel文件
df = pd.read_excel(r'D:\\OneDrive\\桌面\\a.xlsx', engine='openpyxl', usecols=["案件编号", "案件名称", "核验项"])

# 筛选出"联系电话"相关的行
filtered_data = df[df['核验项'].str.strip().str.lower() == '案件编号']

# 获取所有案件编号
all_case_numbers = filtered_data['案件编号']
for i in  all_case_numbers.values:
    print(i[0:9], end=' ')
    print(i[9:10], end=' ')
    print(i[10:18], end=' ')
    if len(i[18:22]) == 3:
        print(i[18:22])
    else:
        print(i[18:22])
