var $e=Object.defineProperty;var et=(p,e,n)=>e in p?$e(p,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):p[e]=n;var v=(p,e,n)=>(et(p,typeof e!="symbol"?e+"":e,n),n);import{_ as tt,u as nt,r as L,a as V,c as st,o as ot,b as k,d as b,e as o,w as _,v as C,f as Re,g as Te,t as g,h as E,i as R,F as ue,j as he,n as fe,k as te}from"./index-05e9acf1.js";import{j as ne}from"./request-7c49b76d.js";function me(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let U=me();function qe(p){U=p}const G={exec:()=>null};function x(p,e=""){let n=typeof p=="string"?p:p.source;const t={replace:(l,i)=>{let s=typeof i=="string"?i:i.source;return s=s.replace(T.caret,"$1"),n=n.replace(l,s),t},getRegex:()=>new RegExp(n,e)};return t}const T={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:p=>new RegExp(`^( {0,3}${p})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:p=>new RegExp(`^ {0,${Math.min(3,p-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:p=>new RegExp(`^ {0,${Math.min(3,p-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:p=>new RegExp(`^ {0,${Math.min(3,p-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:p=>new RegExp(`^ {0,${Math.min(3,p-1)}}#`),htmlBeginRegex:p=>new RegExp(`^ {0,${Math.min(3,p-1)}}<(?:[a-z].*>|!--)`,"i")},it=/^(?:[ \t]*(?:\n|$))+/,lt=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,rt=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,Q=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,at=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,ke=/(?:[*+-]|\d{1,9}[.)])/,De=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Me=x(De).replace(/bull/g,ke).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),ct=x(De).replace(/bull/g,ke).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),be=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,dt=/^[^\n]+/,xe=/(?!\s*\])(?:\\.|[^\[\]\\])+/,pt=x(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",xe).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),ut=x(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,ke).getRegex(),re="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",we=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,ht=x("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",we).replace("tag",re).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Ie=x(be).replace("hr",Q).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",re).getRegex(),ft=x(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Ie).getRegex(),ye={blockquote:ft,code:lt,def:pt,fences:rt,heading:at,hr:Q,html:ht,lheading:Me,list:ut,newline:it,paragraph:Ie,table:G,text:dt},ze=x("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Q).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",re).getRegex(),gt={...ye,lheading:ct,table:ze,paragraph:x(be).replace("hr",Q).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",ze).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",re).getRegex()},mt={...ye,html:x(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",we).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:G,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:x(be).replace("hr",Q).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Me).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},kt=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,bt=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Fe=/^( {2,}|\\)\n(?!\s*$)/,xt=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,ae=/[\p{P}\p{S}]/u,ve=/[\s\p{P}\p{S}]/u,Be=/[^\s\p{P}\p{S}]/u,wt=x(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,ve).getRegex(),je=/(?!~)[\p{P}\p{S}]/u,yt=/(?!~)[\s\p{P}\p{S}]/u,vt=/(?:[^\s\p{P}\p{S}]|~)/u,_t=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Ne=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,St=x(Ne,"u").replace(/punct/g,ae).getRegex(),Ct=x(Ne,"u").replace(/punct/g,je).getRegex(),Ue="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",Rt=x(Ue,"gu").replace(/notPunctSpace/g,Be).replace(/punctSpace/g,ve).replace(/punct/g,ae).getRegex(),Tt=x(Ue,"gu").replace(/notPunctSpace/g,vt).replace(/punctSpace/g,yt).replace(/punct/g,je).getRegex(),zt=x("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Be).replace(/punctSpace/g,ve).replace(/punct/g,ae).getRegex(),Lt=x(/\\(punct)/,"gu").replace(/punct/g,ae).getRegex(),Pt=x(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),At=x(we).replace("(?:-->|$)","-->").getRegex(),Et=x("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",At).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),oe=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,qt=x(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",oe).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Je=x(/^!?\[(label)\]\[(ref)\]/).replace("label",oe).replace("ref",xe).getRegex(),Ve=x(/^!?\[(ref)\](?:\[\])?/).replace("ref",xe).getRegex(),Dt=x("reflink|nolink(?!\\()","g").replace("reflink",Je).replace("nolink",Ve).getRegex(),_e={_backpedal:G,anyPunctuation:Lt,autolink:Pt,blockSkip:_t,br:Fe,code:bt,del:G,emStrongLDelim:St,emStrongRDelimAst:Rt,emStrongRDelimUnd:zt,escape:kt,link:qt,nolink:Ve,punctuation:wt,reflink:Je,reflinkSearch:Dt,tag:Et,text:xt,url:G},Mt={..._e,link:x(/^!?\[(label)\]\((.*?)\)/).replace("label",oe).getRegex(),reflink:x(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",oe).getRegex()},ge={..._e,emStrongRDelimAst:Tt,emStrongLDelim:Ct,url:x(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},It={...ge,br:x(Fe).replace("{2,}","*").getRegex(),text:x(ge.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},se={normal:ye,gfm:gt,pedantic:mt},Z={normal:_e,gfm:ge,breaks:It,pedantic:Mt},Ft={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Le=p=>Ft[p];function M(p,e){if(e){if(T.escapeTest.test(p))return p.replace(T.escapeReplace,Le)}else if(T.escapeTestNoEncode.test(p))return p.replace(T.escapeReplaceNoEncode,Le);return p}function Pe(p){try{p=encodeURI(p).replace(T.percentDecode,"%")}catch{return null}return p}function Ae(p,e){var i;const n=p.replace(T.findPipe,(s,r,d)=>{let a=!1,c=r;for(;--c>=0&&d[c]==="\\";)a=!a;return a?"|":" |"}),t=n.split(T.splitPipe);let l=0;if(t[0].trim()||t.shift(),t.length>0&&!((i=t.at(-1))!=null&&i.trim())&&t.pop(),e)if(t.length>e)t.splice(e);else for(;t.length<e;)t.push("");for(;l<t.length;l++)t[l]=t[l].trim().replace(T.slashPipe,"|");return t}function O(p,e,n){const t=p.length;if(t===0)return"";let l=0;for(;l<t&&p.charAt(t-l-1)===e;)l++;return p.slice(0,t-l)}function Bt(p,e){if(p.indexOf(e[1])===-1)return-1;let n=0;for(let t=0;t<p.length;t++)if(p[t]==="\\")t++;else if(p[t]===e[0])n++;else if(p[t]===e[1]&&(n--,n<0))return t;return n>0?-2:-1}function Ee(p,e,n,t,l){const i=e.href,s=e.title||null,r=p[1].replace(l.other.outputLinkReplace,"$1");t.state.inLink=!0;const d={type:p[0].charAt(0)==="!"?"image":"link",raw:n,href:i,title:s,text:r,tokens:t.inlineTokens(r)};return t.state.inLink=!1,d}function jt(p,e,n){const t=p.match(n.other.indentCodeCompensation);if(t===null)return e;const l=t[1];return e.split(`
`).map(i=>{const s=i.match(n.other.beginningSpace);if(s===null)return i;const[r]=s;return r.length>=l.length?i.slice(l.length):i}).join(`
`)}class ie{constructor(e){v(this,"options");v(this,"rules");v(this,"lexer");this.options=e||U}space(e){const n=this.rules.block.newline.exec(e);if(n&&n[0].length>0)return{type:"space",raw:n[0]}}code(e){const n=this.rules.block.code.exec(e);if(n){const t=n[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:n[0],codeBlockStyle:"indented",text:this.options.pedantic?t:O(t,`
`)}}}fences(e){const n=this.rules.block.fences.exec(e);if(n){const t=n[0],l=jt(t,n[3]||"",this.rules);return{type:"code",raw:t,lang:n[2]?n[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):n[2],text:l}}}heading(e){const n=this.rules.block.heading.exec(e);if(n){let t=n[2].trim();if(this.rules.other.endingHash.test(t)){const l=O(t,"#");(this.options.pedantic||!l||this.rules.other.endingSpaceChar.test(l))&&(t=l.trim())}return{type:"heading",raw:n[0],depth:n[1].length,text:t,tokens:this.lexer.inline(t)}}}hr(e){const n=this.rules.block.hr.exec(e);if(n)return{type:"hr",raw:O(n[0],`
`)}}blockquote(e){const n=this.rules.block.blockquote.exec(e);if(n){let t=O(n[0],`
`).split(`
`),l="",i="";const s=[];for(;t.length>0;){let r=!1;const d=[];let a;for(a=0;a<t.length;a++)if(this.rules.other.blockquoteStart.test(t[a]))d.push(t[a]),r=!0;else if(!r)d.push(t[a]);else break;t=t.slice(a);const c=d.join(`
`),f=c.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");l=l?`${l}
${c}`:c,i=i?`${i}
${f}`:f;const u=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(f,s,!0),this.lexer.state.top=u,t.length===0)break;const m=s.at(-1);if((m==null?void 0:m.type)==="code")break;if((m==null?void 0:m.type)==="blockquote"){const S=m,y=S.raw+`
`+t.join(`
`),z=this.blockquote(y);s[s.length-1]=z,l=l.substring(0,l.length-S.raw.length)+z.raw,i=i.substring(0,i.length-S.text.length)+z.text;break}else if((m==null?void 0:m.type)==="list"){const S=m,y=S.raw+`
`+t.join(`
`),z=this.list(y);s[s.length-1]=z,l=l.substring(0,l.length-m.raw.length)+z.raw,i=i.substring(0,i.length-S.raw.length)+z.raw,t=y.substring(s.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:l,tokens:s,text:i}}}list(e){let n=this.rules.block.list.exec(e);if(n){let t=n[1].trim();const l=t.length>1,i={type:"list",raw:"",ordered:l,start:l?+t.slice(0,-1):"",loose:!1,items:[]};t=l?`\\d{1,9}\\${t.slice(-1)}`:`\\${t}`,this.options.pedantic&&(t=l?t:"[*+-]");const s=this.rules.other.listItemRegex(t);let r=!1;for(;e;){let a=!1,c="",f="";if(!(n=s.exec(e))||this.rules.block.hr.test(e))break;c=n[0],e=e.substring(c.length);let u=n[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,F=>" ".repeat(3*F.length)),m=e.split(`
`,1)[0],S=!u.trim(),y=0;if(this.options.pedantic?(y=2,f=u.trimStart()):S?y=n[1].length+1:(y=n[2].search(this.rules.other.nonSpaceChar),y=y>4?1:y,f=u.slice(y),y+=n[1].length),S&&this.rules.other.blankLine.test(m)&&(c+=m+`
`,e=e.substring(m.length+1),a=!0),!a){const F=this.rules.other.nextBulletRegex(y),B=this.rules.other.hrRegex(y),j=this.rules.other.fencesBeginRegex(y),X=this.rules.other.headingBeginRegex(y),ce=this.rules.other.htmlBeginRegex(y);for(;e;){const J=e.split(`
`,1)[0];let I;if(m=J,this.options.pedantic?(m=m.replace(this.rules.other.listReplaceNesting,"  "),I=m):I=m.replace(this.rules.other.tabCharGlobal,"    "),j.test(m)||X.test(m)||ce.test(m)||F.test(m)||B.test(m))break;if(I.search(this.rules.other.nonSpaceChar)>=y||!m.trim())f+=`
`+I.slice(y);else{if(S||u.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||j.test(u)||X.test(u)||B.test(u))break;f+=`
`+m}!S&&!m.trim()&&(S=!0),c+=J+`
`,e=e.substring(J.length+1),u=I.slice(y)}}i.loose||(r?i.loose=!0:this.rules.other.doubleBlankLine.test(c)&&(r=!0));let z=null,Y;this.options.gfm&&(z=this.rules.other.listIsTask.exec(f),z&&(Y=z[0]!=="[ ] ",f=f.replace(this.rules.other.listReplaceTask,""))),i.items.push({type:"list_item",raw:c,task:!!z,checked:Y,loose:!1,text:f,tokens:[]}),i.raw+=c}const d=i.items.at(-1);if(d)d.raw=d.raw.trimEnd(),d.text=d.text.trimEnd();else return;i.raw=i.raw.trimEnd();for(let a=0;a<i.items.length;a++)if(this.lexer.state.top=!1,i.items[a].tokens=this.lexer.blockTokens(i.items[a].text,[]),!i.loose){const c=i.items[a].tokens.filter(u=>u.type==="space"),f=c.length>0&&c.some(u=>this.rules.other.anyLine.test(u.raw));i.loose=f}if(i.loose)for(let a=0;a<i.items.length;a++)i.items[a].loose=!0;return i}}html(e){const n=this.rules.block.html.exec(e);if(n)return{type:"html",block:!0,raw:n[0],pre:n[1]==="pre"||n[1]==="script"||n[1]==="style",text:n[0]}}def(e){const n=this.rules.block.def.exec(e);if(n){const t=n[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),l=n[2]?n[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=n[3]?n[3].substring(1,n[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):n[3];return{type:"def",tag:t,raw:n[0],href:l,title:i}}}table(e){var r;const n=this.rules.block.table.exec(e);if(!n||!this.rules.other.tableDelimiter.test(n[2]))return;const t=Ae(n[1]),l=n[2].replace(this.rules.other.tableAlignChars,"").split("|"),i=(r=n[3])!=null&&r.trim()?n[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],s={type:"table",raw:n[0],header:[],align:[],rows:[]};if(t.length===l.length){for(const d of l)this.rules.other.tableAlignRight.test(d)?s.align.push("right"):this.rules.other.tableAlignCenter.test(d)?s.align.push("center"):this.rules.other.tableAlignLeft.test(d)?s.align.push("left"):s.align.push(null);for(let d=0;d<t.length;d++)s.header.push({text:t[d],tokens:this.lexer.inline(t[d]),header:!0,align:s.align[d]});for(const d of i)s.rows.push(Ae(d,s.header.length).map((a,c)=>({text:a,tokens:this.lexer.inline(a),header:!1,align:s.align[c]})));return s}}lheading(e){const n=this.rules.block.lheading.exec(e);if(n)return{type:"heading",raw:n[0],depth:n[2].charAt(0)==="="?1:2,text:n[1],tokens:this.lexer.inline(n[1])}}paragraph(e){const n=this.rules.block.paragraph.exec(e);if(n){const t=n[1].charAt(n[1].length-1)===`
`?n[1].slice(0,-1):n[1];return{type:"paragraph",raw:n[0],text:t,tokens:this.lexer.inline(t)}}}text(e){const n=this.rules.block.text.exec(e);if(n)return{type:"text",raw:n[0],text:n[0],tokens:this.lexer.inline(n[0])}}escape(e){const n=this.rules.inline.escape.exec(e);if(n)return{type:"escape",raw:n[0],text:n[1]}}tag(e){const n=this.rules.inline.tag.exec(e);if(n)return!this.lexer.state.inLink&&this.rules.other.startATag.test(n[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(n[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(n[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(n[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:n[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:n[0]}}link(e){const n=this.rules.inline.link.exec(e);if(n){const t=n[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(t)){if(!this.rules.other.endAngleBracket.test(t))return;const s=O(t.slice(0,-1),"\\");if((t.length-s.length)%2===0)return}else{const s=Bt(n[2],"()");if(s===-2)return;if(s>-1){const d=(n[0].indexOf("!")===0?5:4)+n[1].length+s;n[2]=n[2].substring(0,s),n[0]=n[0].substring(0,d).trim(),n[3]=""}}let l=n[2],i="";if(this.options.pedantic){const s=this.rules.other.pedanticHrefTitle.exec(l);s&&(l=s[1],i=s[3])}else i=n[3]?n[3].slice(1,-1):"";return l=l.trim(),this.rules.other.startAngleBracket.test(l)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(t)?l=l.slice(1):l=l.slice(1,-1)),Ee(n,{href:l&&l.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},n[0],this.lexer,this.rules)}}reflink(e,n){let t;if((t=this.rules.inline.reflink.exec(e))||(t=this.rules.inline.nolink.exec(e))){const l=(t[2]||t[1]).replace(this.rules.other.multipleSpaceGlobal," "),i=n[l.toLowerCase()];if(!i){const s=t[0].charAt(0);return{type:"text",raw:s,text:s}}return Ee(t,i,t[0],this.lexer,this.rules)}}emStrong(e,n,t=""){let l=this.rules.inline.emStrongLDelim.exec(e);if(!l||l[3]&&t.match(this.rules.other.unicodeAlphaNumeric))return;if(!(l[1]||l[2]||"")||!t||this.rules.inline.punctuation.exec(t)){const s=[...l[0]].length-1;let r,d,a=s,c=0;const f=l[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(f.lastIndex=0,n=n.slice(-1*e.length+s);(l=f.exec(n))!=null;){if(r=l[1]||l[2]||l[3]||l[4]||l[5]||l[6],!r)continue;if(d=[...r].length,l[3]||l[4]){a+=d;continue}else if((l[5]||l[6])&&s%3&&!((s+d)%3)){c+=d;continue}if(a-=d,a>0)continue;d=Math.min(d,d+a+c);const u=[...l[0]][0].length,m=e.slice(0,s+l.index+u+d);if(Math.min(s,d)%2){const y=m.slice(1,-1);return{type:"em",raw:m,text:y,tokens:this.lexer.inlineTokens(y)}}const S=m.slice(2,-2);return{type:"strong",raw:m,text:S,tokens:this.lexer.inlineTokens(S)}}}}codespan(e){const n=this.rules.inline.code.exec(e);if(n){let t=n[2].replace(this.rules.other.newLineCharGlobal," ");const l=this.rules.other.nonSpaceChar.test(t),i=this.rules.other.startingSpaceChar.test(t)&&this.rules.other.endingSpaceChar.test(t);return l&&i&&(t=t.substring(1,t.length-1)),{type:"codespan",raw:n[0],text:t}}}br(e){const n=this.rules.inline.br.exec(e);if(n)return{type:"br",raw:n[0]}}del(e){const n=this.rules.inline.del.exec(e);if(n)return{type:"del",raw:n[0],text:n[2],tokens:this.lexer.inlineTokens(n[2])}}autolink(e){const n=this.rules.inline.autolink.exec(e);if(n){let t,l;return n[2]==="@"?(t=n[1],l="mailto:"+t):(t=n[1],l=t),{type:"link",raw:n[0],text:t,href:l,tokens:[{type:"text",raw:t,text:t}]}}}url(e){var t;let n;if(n=this.rules.inline.url.exec(e)){let l,i;if(n[2]==="@")l=n[0],i="mailto:"+l;else{let s;do s=n[0],n[0]=((t=this.rules.inline._backpedal.exec(n[0]))==null?void 0:t[0])??"";while(s!==n[0]);l=n[0],n[1]==="www."?i="http://"+n[0]:i=n[0]}return{type:"link",raw:n[0],text:l,href:i,tokens:[{type:"text",raw:l,text:l}]}}}inlineText(e){const n=this.rules.inline.text.exec(e);if(n){const t=this.lexer.state.inRawBlock;return{type:"text",raw:n[0],text:n[0],escaped:t}}}}class P{constructor(e){v(this,"tokens");v(this,"options");v(this,"state");v(this,"tokenizer");v(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||U,this.options.tokenizer=this.options.tokenizer||new ie,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const n={other:T,block:se.normal,inline:Z.normal};this.options.pedantic?(n.block=se.pedantic,n.inline=Z.pedantic):this.options.gfm&&(n.block=se.gfm,this.options.breaks?n.inline=Z.breaks:n.inline=Z.gfm),this.tokenizer.rules=n}static get rules(){return{block:se,inline:Z}}static lex(e,n){return new P(n).lex(e)}static lexInline(e,n){return new P(n).inlineTokens(e)}lex(e){e=e.replace(T.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let n=0;n<this.inlineQueue.length;n++){const t=this.inlineQueue[n];this.inlineTokens(t.src,t.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,n=[],t=!1){var l,i,s;for(this.options.pedantic&&(e=e.replace(T.tabCharGlobal,"    ").replace(T.spaceLine,""));e;){let r;if((i=(l=this.options.extensions)==null?void 0:l.block)!=null&&i.some(a=>(r=a.call({lexer:this},e,n))?(e=e.substring(r.raw.length),n.push(r),!0):!1))continue;if(r=this.tokenizer.space(e)){e=e.substring(r.raw.length);const a=n.at(-1);r.raw.length===1&&a!==void 0?a.raw+=`
`:n.push(r);continue}if(r=this.tokenizer.code(e)){e=e.substring(r.raw.length);const a=n.at(-1);(a==null?void 0:a.type)==="paragraph"||(a==null?void 0:a.type)==="text"?(a.raw+=`
`+r.raw,a.text+=`
`+r.text,this.inlineQueue.at(-1).src=a.text):n.push(r);continue}if(r=this.tokenizer.fences(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.heading(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.hr(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.blockquote(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.list(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.html(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.def(e)){e=e.substring(r.raw.length);const a=n.at(-1);(a==null?void 0:a.type)==="paragraph"||(a==null?void 0:a.type)==="text"?(a.raw+=`
`+r.raw,a.text+=`
`+r.raw,this.inlineQueue.at(-1).src=a.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.lheading(e)){e=e.substring(r.raw.length),n.push(r);continue}let d=e;if((s=this.options.extensions)!=null&&s.startBlock){let a=1/0;const c=e.slice(1);let f;this.options.extensions.startBlock.forEach(u=>{f=u.call({lexer:this},c),typeof f=="number"&&f>=0&&(a=Math.min(a,f))}),a<1/0&&a>=0&&(d=e.substring(0,a+1))}if(this.state.top&&(r=this.tokenizer.paragraph(d))){const a=n.at(-1);t&&(a==null?void 0:a.type)==="paragraph"?(a.raw+=`
`+r.raw,a.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=a.text):n.push(r),t=d.length!==e.length,e=e.substring(r.raw.length);continue}if(r=this.tokenizer.text(e)){e=e.substring(r.raw.length);const a=n.at(-1);(a==null?void 0:a.type)==="text"?(a.raw+=`
`+r.raw,a.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=a.text):n.push(r);continue}if(e){const a="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(a);break}else throw new Error(a)}}return this.state.top=!0,n}inline(e,n=[]){return this.inlineQueue.push({src:e,tokens:n}),n}inlineTokens(e,n=[]){var r,d,a;let t=e,l=null;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(l=this.tokenizer.rules.inline.reflinkSearch.exec(t))!=null;)c.includes(l[0].slice(l[0].lastIndexOf("[")+1,-1))&&(t=t.slice(0,l.index)+"["+"a".repeat(l[0].length-2)+"]"+t.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(l=this.tokenizer.rules.inline.anyPunctuation.exec(t))!=null;)t=t.slice(0,l.index)+"++"+t.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(l=this.tokenizer.rules.inline.blockSkip.exec(t))!=null;)t=t.slice(0,l.index)+"["+"a".repeat(l[0].length-2)+"]"+t.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let i=!1,s="";for(;e;){i||(s=""),i=!1;let c;if((d=(r=this.options.extensions)==null?void 0:r.inline)!=null&&d.some(u=>(c=u.call({lexer:this},e,n))?(e=e.substring(c.raw.length),n.push(c),!0):!1))continue;if(c=this.tokenizer.escape(e)){e=e.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.tag(e)){e=e.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.link(e)){e=e.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(c.raw.length);const u=n.at(-1);c.type==="text"&&(u==null?void 0:u.type)==="text"?(u.raw+=c.raw,u.text+=c.text):n.push(c);continue}if(c=this.tokenizer.emStrong(e,t,s)){e=e.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.codespan(e)){e=e.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.br(e)){e=e.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.del(e)){e=e.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.autolink(e)){e=e.substring(c.raw.length),n.push(c);continue}if(!this.state.inLink&&(c=this.tokenizer.url(e))){e=e.substring(c.raw.length),n.push(c);continue}let f=e;if((a=this.options.extensions)!=null&&a.startInline){let u=1/0;const m=e.slice(1);let S;this.options.extensions.startInline.forEach(y=>{S=y.call({lexer:this},m),typeof S=="number"&&S>=0&&(u=Math.min(u,S))}),u<1/0&&u>=0&&(f=e.substring(0,u+1))}if(c=this.tokenizer.inlineText(f)){e=e.substring(c.raw.length),c.raw.slice(-1)!=="_"&&(s=c.raw.slice(-1)),i=!0;const u=n.at(-1);(u==null?void 0:u.type)==="text"?(u.raw+=c.raw,u.text+=c.text):n.push(c);continue}if(e){const u="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(u);break}else throw new Error(u)}}return n}}class le{constructor(e){v(this,"options");v(this,"parser");this.options=e||U}space(e){return""}code({text:e,lang:n,escaped:t}){var s;const l=(s=(n||"").match(T.notSpaceStart))==null?void 0:s[0],i=e.replace(T.endingNewline,"")+`
`;return l?'<pre><code class="language-'+M(l)+'">'+(t?i:M(i,!0))+`</code></pre>
`:"<pre><code>"+(t?i:M(i,!0))+`</code></pre>
`}blockquote({tokens:e}){return`<blockquote>
${this.parser.parse(e)}</blockquote>
`}html({text:e}){return e}heading({tokens:e,depth:n}){return`<h${n}>${this.parser.parseInline(e)}</h${n}>
`}hr(e){return`<hr>
`}list(e){const n=e.ordered,t=e.start;let l="";for(let r=0;r<e.items.length;r++){const d=e.items[r];l+=this.listitem(d)}const i=n?"ol":"ul",s=n&&t!==1?' start="'+t+'"':"";return"<"+i+s+`>
`+l+"</"+i+`>
`}listitem(e){var t;let n="";if(e.task){const l=this.checkbox({checked:!!e.checked});e.loose?((t=e.tokens[0])==null?void 0:t.type)==="paragraph"?(e.tokens[0].text=l+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&e.tokens[0].tokens[0].type==="text"&&(e.tokens[0].tokens[0].text=l+" "+M(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:l+" ",text:l+" ",escaped:!0}):n+=l+" "}return n+=this.parser.parse(e.tokens,!!e.loose),`<li>${n}</li>
`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>
`}table(e){let n="",t="";for(let i=0;i<e.header.length;i++)t+=this.tablecell(e.header[i]);n+=this.tablerow({text:t});let l="";for(let i=0;i<e.rows.length;i++){const s=e.rows[i];t="";for(let r=0;r<s.length;r++)t+=this.tablecell(s[r]);l+=this.tablerow({text:t})}return l&&(l=`<tbody>${l}</tbody>`),`<table>
<thead>
`+n+`</thead>
`+l+`</table>
`}tablerow({text:e}){return`<tr>
${e}</tr>
`}tablecell(e){const n=this.parser.parseInline(e.tokens),t=e.header?"th":"td";return(e.align?`<${t} align="${e.align}">`:`<${t}>`)+n+`</${t}>
`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${M(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:n,tokens:t}){const l=this.parser.parseInline(t),i=Pe(e);if(i===null)return l;e=i;let s='<a href="'+e+'"';return n&&(s+=' title="'+M(n)+'"'),s+=">"+l+"</a>",s}image({href:e,title:n,text:t,tokens:l}){l&&(t=this.parser.parseInline(l,this.parser.textRenderer));const i=Pe(e);if(i===null)return M(t);e=i;let s=`<img src="${e}" alt="${t}"`;return n&&(s+=` title="${M(n)}"`),s+=">",s}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:M(e.text)}}class Se{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}}class A{constructor(e){v(this,"options");v(this,"renderer");v(this,"textRenderer");this.options=e||U,this.options.renderer=this.options.renderer||new le,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new Se}static parse(e,n){return new A(n).parse(e)}static parseInline(e,n){return new A(n).parseInline(e)}parse(e,n=!0){var l,i;let t="";for(let s=0;s<e.length;s++){const r=e[s];if((i=(l=this.options.extensions)==null?void 0:l.renderers)!=null&&i[r.type]){const a=r,c=this.options.extensions.renderers[a.type].call({parser:this},a);if(c!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(a.type)){t+=c||"";continue}}const d=r;switch(d.type){case"space":{t+=this.renderer.space(d);continue}case"hr":{t+=this.renderer.hr(d);continue}case"heading":{t+=this.renderer.heading(d);continue}case"code":{t+=this.renderer.code(d);continue}case"table":{t+=this.renderer.table(d);continue}case"blockquote":{t+=this.renderer.blockquote(d);continue}case"list":{t+=this.renderer.list(d);continue}case"html":{t+=this.renderer.html(d);continue}case"paragraph":{t+=this.renderer.paragraph(d);continue}case"text":{let a=d,c=this.renderer.text(a);for(;s+1<e.length&&e[s+1].type==="text";)a=e[++s],c+=`
`+this.renderer.text(a);n?t+=this.renderer.paragraph({type:"paragraph",raw:c,text:c,tokens:[{type:"text",raw:c,text:c,escaped:!0}]}):t+=c;continue}default:{const a='Token with "'+d.type+'" type was not found.';if(this.options.silent)return console.error(a),"";throw new Error(a)}}}return t}parseInline(e,n=this.renderer){var l,i;let t="";for(let s=0;s<e.length;s++){const r=e[s];if((i=(l=this.options.extensions)==null?void 0:l.renderers)!=null&&i[r.type]){const a=this.options.extensions.renderers[r.type].call({parser:this},r);if(a!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(r.type)){t+=a||"";continue}}const d=r;switch(d.type){case"escape":{t+=n.text(d);break}case"html":{t+=n.html(d);break}case"link":{t+=n.link(d);break}case"image":{t+=n.image(d);break}case"strong":{t+=n.strong(d);break}case"em":{t+=n.em(d);break}case"codespan":{t+=n.codespan(d);break}case"br":{t+=n.br(d);break}case"del":{t+=n.del(d);break}case"text":{t+=n.text(d);break}default:{const a='Token with "'+d.type+'" type was not found.';if(this.options.silent)return console.error(a),"";throw new Error(a)}}}return t}}class H{constructor(e){v(this,"options");v(this,"block");this.options=e||U}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?P.lex:P.lexInline}provideParser(){return this.block?A.parse:A.parseInline}}v(H,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));class Nt{constructor(...e){v(this,"defaults",me());v(this,"options",this.setOptions);v(this,"parse",this.parseMarkdown(!0));v(this,"parseInline",this.parseMarkdown(!1));v(this,"Parser",A);v(this,"Renderer",le);v(this,"TextRenderer",Se);v(this,"Lexer",P);v(this,"Tokenizer",ie);v(this,"Hooks",H);this.use(...e)}walkTokens(e,n){var l,i;let t=[];for(const s of e)switch(t=t.concat(n.call(this,s)),s.type){case"table":{const r=s;for(const d of r.header)t=t.concat(this.walkTokens(d.tokens,n));for(const d of r.rows)for(const a of d)t=t.concat(this.walkTokens(a.tokens,n));break}case"list":{const r=s;t=t.concat(this.walkTokens(r.items,n));break}default:{const r=s;(i=(l=this.defaults.extensions)==null?void 0:l.childTokens)!=null&&i[r.type]?this.defaults.extensions.childTokens[r.type].forEach(d=>{const a=r[d].flat(1/0);t=t.concat(this.walkTokens(a,n))}):r.tokens&&(t=t.concat(this.walkTokens(r.tokens,n)))}}return t}use(...e){const n=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(t=>{const l={...t};if(l.async=this.defaults.async||l.async||!1,t.extensions&&(t.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if("renderer"in i){const s=n.renderers[i.name];s?n.renderers[i.name]=function(...r){let d=i.renderer.apply(this,r);return d===!1&&(d=s.apply(this,r)),d}:n.renderers[i.name]=i.renderer}if("tokenizer"in i){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const s=n[i.level];s?s.unshift(i.tokenizer):n[i.level]=[i.tokenizer],i.start&&(i.level==="block"?n.startBlock?n.startBlock.push(i.start):n.startBlock=[i.start]:i.level==="inline"&&(n.startInline?n.startInline.push(i.start):n.startInline=[i.start]))}"childTokens"in i&&i.childTokens&&(n.childTokens[i.name]=i.childTokens)}),l.extensions=n),t.renderer){const i=this.defaults.renderer||new le(this.defaults);for(const s in t.renderer){if(!(s in i))throw new Error(`renderer '${s}' does not exist`);if(["options","parser"].includes(s))continue;const r=s,d=t.renderer[r],a=i[r];i[r]=(...c)=>{let f=d.apply(i,c);return f===!1&&(f=a.apply(i,c)),f||""}}l.renderer=i}if(t.tokenizer){const i=this.defaults.tokenizer||new ie(this.defaults);for(const s in t.tokenizer){if(!(s in i))throw new Error(`tokenizer '${s}' does not exist`);if(["options","rules","lexer"].includes(s))continue;const r=s,d=t.tokenizer[r],a=i[r];i[r]=(...c)=>{let f=d.apply(i,c);return f===!1&&(f=a.apply(i,c)),f}}l.tokenizer=i}if(t.hooks){const i=this.defaults.hooks||new H;for(const s in t.hooks){if(!(s in i))throw new Error(`hook '${s}' does not exist`);if(["options","block"].includes(s))continue;const r=s,d=t.hooks[r],a=i[r];H.passThroughHooks.has(s)?i[r]=c=>{if(this.defaults.async)return Promise.resolve(d.call(i,c)).then(u=>a.call(i,u));const f=d.call(i,c);return a.call(i,f)}:i[r]=(...c)=>{let f=d.apply(i,c);return f===!1&&(f=a.apply(i,c)),f}}l.hooks=i}if(t.walkTokens){const i=this.defaults.walkTokens,s=t.walkTokens;l.walkTokens=function(r){let d=[];return d.push(s.call(this,r)),i&&(d=d.concat(i.call(this,r))),d}}this.defaults={...this.defaults,...l}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,n){return P.lex(e,n??this.defaults)}parser(e,n){return A.parse(e,n??this.defaults)}parseMarkdown(e){return(t,l)=>{const i={...l},s={...this.defaults,...i},r=this.onError(!!s.silent,!!s.async);if(this.defaults.async===!0&&i.async===!1)return r(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof t>"u"||t===null)return r(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return r(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));s.hooks&&(s.hooks.options=s,s.hooks.block=e);const d=s.hooks?s.hooks.provideLexer():e?P.lex:P.lexInline,a=s.hooks?s.hooks.provideParser():e?A.parse:A.parseInline;if(s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(t):t).then(c=>d(c,s)).then(c=>s.hooks?s.hooks.processAllTokens(c):c).then(c=>s.walkTokens?Promise.all(this.walkTokens(c,s.walkTokens)).then(()=>c):c).then(c=>a(c,s)).then(c=>s.hooks?s.hooks.postprocess(c):c).catch(r);try{s.hooks&&(t=s.hooks.preprocess(t));let c=d(t,s);s.hooks&&(c=s.hooks.processAllTokens(c)),s.walkTokens&&this.walkTokens(c,s.walkTokens);let f=a(c,s);return s.hooks&&(f=s.hooks.postprocess(f)),f}catch(c){return r(c)}}}onError(e,n){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const l="<p>An error occurred:</p><pre>"+M(t.message+"",!0)+"</pre>";return n?Promise.resolve(l):l}if(n)return Promise.reject(t);throw t}}}const N=new Nt;function w(p,e){return N.parse(p,e)}w.options=w.setOptions=function(p){return N.setOptions(p),w.defaults=N.defaults,qe(w.defaults),w};w.getDefaults=me;w.defaults=U;w.use=function(...p){return N.use(...p),w.defaults=N.defaults,qe(w.defaults),w};w.walkTokens=function(p,e){return N.walkTokens(p,e)};w.parseInline=N.parseInline;w.Parser=A;w.parser=A.parse;w.Renderer=le;w.TextRenderer=Se;w.Lexer=P;w.lexer=P.lex;w.Tokenizer=ie;w.Hooks=H;w.parse=w;w.options;w.setOptions;w.use;w.walkTokens;w.parseInline;A.parse;P.lex;const Ut={name:"JobList",setup(){const p=nt(),e=L(!1),n=L([]),t=V({company_name:"",position_name:"",work_location:"",education_req:"",industry:"",major_req:""}),l=V({field:"",order:"asc"}),i=V({currentPage:1,pageSize:10}),s=V({jobs:[],total:0,total_pages:0,page:1,size:10}),r=st(()=>{const h=s.total_pages||0,q=i.currentPage;if(h<=5)return Array.from({length:h},(Ke,pe)=>pe+1);const D=Math.max(1,q-2),We=Math.min(h,D+4);return Array.from({length:We-D+1},(Ke,pe)=>D+pe)}),d=h=>!h||h==="0001-01-01 00:00:00"?"长期招聘":h,a=L(null),c=h=>{a.value=h},f=()=>{a.value=null},u=async()=>{e.value=!0;try{const h={page:i.currentPage,size:i.pageSize};l.field&&(h.sort_field=l.field,h.sort_order=l.order),Object.keys(t).forEach(D=>{t[D]&&t[D].trim()!==""&&(h[D]=t[D])});const q=await ne.getJobs(h);Object.assign(s,q.data),i.currentPage=q.data.page||1,n.value=[]}catch(h){console.error("获取岗位数据失败",h)}finally{e.value=!1}},m=()=>{i.currentPage=1,u()},S=()=>{Object.keys(t).forEach(h=>{t[h]=""}),l.field="",l.order="asc",i.currentPage=1,u()},y=h=>{l.field===h?l.order=l.order==="asc"?"desc":"asc":(l.field=h,l.order="asc"),u()},z=h=>{h<1||h>s.total_pages||h===i.currentPage||(i.currentPage=h,u(),window.scrollTo({top:0,behavior:"smooth"}))},Y=h=>w(h),F=L(!1),B=L(!1),j=V({company_name:"",company_type:"",industry:"",position_name:"",work_location:"",education_req:"",major_req:"",salary:"",job_type:"",position_type:"",work_type:"",post_date:"",end_date:"",position_link:"",backup_link:"",position_desc:"",position_requirement:""}),X=h=>{Object.keys(j).forEach(q=>{j[q]=h[q]||""}),F.value=!0},ce=()=>{B.value||(F.value=!1)},J=async()=>{if(!(!a.value||!a.value.id))try{B.value=!0;const h=await ne.updateJob(a.value.id,j);a.value=h.data;const q=s.jobs.findIndex(D=>D.id===h.data.id);q!==-1&&(s.jobs[q]=h.data),F.value=!1,alert("岗位信息更新成功")}catch(h){console.error("更新岗位信息失败",h),alert(`更新失败: ${h.message||"未知错误"}`)}finally{B.value=!1}},I=L(!1),Ce=L([]),de=L(!1),W=L(!1),K=L(""),$=L(""),ee=L(!1),Ze=async()=>{I.value=!0,await Ge()},Oe=()=>{I.value=!1},Ge=async()=>{de.value=!0;try{const h=await ne.getBackupLinks();Ce.value=h.data.domains||[]}catch(h){console.error("获取域名列表失败",h),alert("获取域名列表失败："+(h.message||"未知错误"))}finally{de.value=!1}},He=h=>{K.value=h,$.value="",W.value=!0},Qe=()=>{ee.value||(W.value=!1)},Ye=async()=>{if(!K.value||!$.value.trim()){alert("请输入有效的公司名称");return}ee.value=!0;try{await ne.updateJobNameByUrl({url:K.value,name:$.value.trim()}),alert("公司名称更新成功"),W.value=!1,u()}catch(h){console.error("更新公司名称失败",h),alert("更新失败："+(h.message||"未知错误"))}finally{ee.value=!1}},Xe=()=>{p.push("/url-manager")};return ot(()=>{u()}),{loading:e,searchForm:t,sortConfig:l,pagination:i,jobListData:s,paginationPages:r,selectedJob:a,showEditModal:F,isSaving:B,editForm:j,formatDate:d,renderMarkdown:Y,showDetails:c,closeDetails:f,openEditJob:X,closeEditModal:ce,saveJob:J,handleSearch:m,handleReset:S,handleSort:y,handlePageChange:z,showDomainModal:I,domainList:Ce,domainLoading:de,showNameEditor:W,currentDomain:K,newCompanyName:$,isUpdating:ee,showDomainManager:Ze,closeDomainModal:Oe,openNameEditor:He,closeNameEditor:Qe,updateCompanyName:Ye,goToUrlManager:Xe}}},Jt={class:"job-list-container"},Vt={class:"page-header"},Zt={class:"search-panel"},Ot={class:"form-row"},Gt={class:"form-group"},Ht={class:"form-group"},Qt={class:"form-group"},Yt={class:"form-row"},Xt={class:"form-group"},Wt={class:"form-group"},Kt={class:"form-group"},$t={class:"buttons"},en={class:"data-panel"},tn={class:"stats"},nn={class:"stat-item"},sn={class:"stat-value"},on={class:"stat-item"},ln={class:"stat-value"},rn={class:"stat-item"},an={class:"stat-value"},cn={class:"stat-item"},dn={class:"stat-value"},pn={class:"table-wrapper"},un={key:0,class:"data-table"},hn={key:0,class:"sort-icon"},fn={key:0,class:"sort-icon"},gn={key:0,class:"sort-icon"},mn={key:0,class:"sort-icon"},kn={key:0,class:"sort-icon"},bn={key:0,class:"sort-icon"},xn={key:0,class:"sort-icon"},wn={key:0,class:"sort-icon"},yn={key:0,class:"sort-icon"},vn={key:0,class:"sort-icon"},_n={key:0,class:"sort-icon"},Sn={class:"position-name ellipsis"},Cn={class:"company-name ellipsis"},Rn={class:"ellipsis"},Tn={class:"ellipsis"},zn={class:"ellipsis"},Ln={class:"ellipsis"},Pn={class:"ellipsis"},An={class:"ellipsis"},En={class:"action-buttons"},qn=["href"],Dn=["onClick"],Mn={key:1,class:"no-results"},In={key:0,class:"pagination"},Fn=["onClick"],Bn={class:"details-modal-content"},jn={class:"details-modal-header"},Nn={class:"job-title"},Un={class:"job-meta"},Jn={class:"company-name"},Vn={class:"salary"},Zn={class:"job-tags"},On={class:"tag"},Gn={class:"tag"},Hn={class:"tag"},Qn={class:"details-modal-body"},Yn={class:"details-section"},Xn=["innerHTML"],Wn={class:"details-section"},Kn=["innerHTML"],$n={class:"details-grid"},es={class:"details-item"},ts={class:"details-value"},ns={class:"details-item"},ss={class:"details-value"},os={class:"details-item"},is={class:"details-value"},ls={class:"details-item"},rs={class:"details-value"},as={class:"details-item"},cs={class:"details-value"},ds={class:"details-item"},ps={class:"details-value"},us={class:"details-actions"},hs=["href"],fs=["href"],gs={class:"edit-modal-content"},ms={class:"edit-modal-body"},ks={class:"form-grid"},bs={class:"form-group"},xs={class:"form-group"},ws={class:"form-group"},ys={class:"form-group"},vs={class:"form-group"},_s={class:"form-group"},Ss={class:"form-group"},Cs={class:"form-group"},Rs={class:"form-group"},Ts={class:"form-group"},zs={class:"form-group"},Ls={class:"form-group"},Ps={class:"form-group"},As={class:"form-group full-width"},Es={class:"form-group full-width"},qs={class:"form-group full-width"},Ds={class:"form-group full-width"},Ms={class:"form-actions"},Is=["disabled"],Fs={class:"domain-modal-content"},Bs={class:"domain-modal-body"},js={key:0,class:"domain-loading"},Ns={key:1,class:"no-domains"},Us={key:2,class:"domain-list"},Js={class:"domain-name"},Vs=["onClick"],Zs={class:"name-editor-content"},Os={class:"name-editor-body"},Gs={class:"name-editor-info"},Hs={class:"info-item"},Qs={class:"info-value"},Ys={class:"name-editor-form"},Xs={class:"form-group"},Ws={class:"form-actions"},Ks=["disabled"],$s={key:4,class:"loading"};function eo(p,e,n,t,l,i){return k(),b("div",Jt,[o("header",Vt,[e[54]||(e[54]=o("h1",null,"智能岗位查询系统",-1)),e[55]||(e[55]=o("p",{class:"sub-title"},"快速找到适合您的理想职位",-1)),o("button",{class:"url-manager-btn",onClick:e[0]||(e[0]=(...s)=>t.goToUrlManager&&t.goToUrlManager(...s))},"URL管理")]),o("section",Zt,[o("div",Ot,[o("div",Gt,[e[56]||(e[56]=o("label",{for:"company_name"},"公司名称",-1)),_(o("input",{type:"text",id:"company_name","onUpdate:modelValue":e[1]||(e[1]=s=>t.searchForm.company_name=s),placeholder:"输入公司名称"},null,512),[[C,t.searchForm.company_name]])]),o("div",Ht,[e[57]||(e[57]=o("label",{for:"position_name"},"岗位名称",-1)),_(o("input",{type:"text",id:"position_name","onUpdate:modelValue":e[2]||(e[2]=s=>t.searchForm.position_name=s),placeholder:"输入岗位名称"},null,512),[[C,t.searchForm.position_name]])]),o("div",Qt,[e[58]||(e[58]=o("label",{for:"work_location"},"工作地点",-1)),_(o("input",{type:"text",id:"work_location","onUpdate:modelValue":e[3]||(e[3]=s=>t.searchForm.work_location=s),placeholder:"输入工作地点"},null,512),[[C,t.searchForm.work_location]])])]),o("div",Yt,[o("div",Xt,[e[60]||(e[60]=o("label",{for:"education_req"},"学历要求",-1)),_(o("select",{id:"education_req","onUpdate:modelValue":e[4]||(e[4]=s=>t.searchForm.education_req=s)},e[59]||(e[59]=[Te('<option value="" data-v-7f5e8fa6>不限</option><option value="大专" data-v-7f5e8fa6>大专</option><option value="本科" data-v-7f5e8fa6>本科</option><option value="硕士" data-v-7f5e8fa6>硕士</option><option value="博士" data-v-7f5e8fa6>博士</option>',5)]),512),[[Re,t.searchForm.education_req]])]),o("div",Wt,[e[61]||(e[61]=o("label",{for:"industry"},"行业",-1)),_(o("input",{type:"text",id:"industry","onUpdate:modelValue":e[5]||(e[5]=s=>t.searchForm.industry=s),placeholder:"输入行业"},null,512),[[C,t.searchForm.industry]])]),o("div",Kt,[e[62]||(e[62]=o("label",{for:"major_req"},"专业要求",-1)),_(o("input",{type:"text",id:"major_req","onUpdate:modelValue":e[6]||(e[6]=s=>t.searchForm.major_req=s),placeholder:"输入专业要求"},null,512),[[C,t.searchForm.major_req]])])]),o("div",$t,[o("button",{class:"btn-primary",onClick:e[7]||(e[7]=(...s)=>t.handleSearch&&t.handleSearch(...s))},"搜索岗位"),o("button",{class:"btn-secondary",onClick:e[8]||(e[8]=(...s)=>t.handleReset&&t.handleReset(...s))},"重置条件"),o("button",{class:"btn-secondary",onClick:e[9]||(e[9]=(...s)=>t.showDomainManager&&t.showDomainManager(...s))},"批量修改公司名称")])]),o("section",en,[o("div",tn,[o("div",nn,[o("div",sn,g(t.jobListData.total||0),1),e[63]||(e[63]=o("div",{class:"stat-label"},"岗位总数",-1))]),o("div",on,[o("div",ln,g(t.jobListData.total_pages||0),1),e[64]||(e[64]=o("div",{class:"stat-label"},"总页数",-1))]),o("div",rn,[o("div",an,g(t.pagination.currentPage),1),e[65]||(e[65]=o("div",{class:"stat-label"},"当前页",-1))]),o("div",cn,[o("div",dn,g(t.pagination.pageSize),1),e[66]||(e[66]=o("div",{class:"stat-label"},"每页条数",-1))])]),o("div",pn,[t.jobListData.jobs&&t.jobListData.jobs.length>0?(k(),b("table",un,[o("thead",null,[o("tr",null,[o("th",{onClick:e[10]||(e[10]=s=>t.handleSort("id")),class:"th-id"},[e[67]||(e[67]=E(" ID ")),t.sortConfig.field==="id"?(k(),b("span",hn,g(t.sortConfig.order==="asc"?"▲":"▼"),1)):R("",!0)]),o("th",{onClick:e[11]||(e[11]=s=>t.handleSort("position_name")),class:"th-major"},[e[68]||(e[68]=E(" 岗位名称 ")),t.sortConfig.field==="position_name"?(k(),b("span",fn,g(t.sortConfig.order==="asc"?"▲":"▼"),1)):R("",!0)]),o("th",{onClick:e[12]||(e[12]=s=>t.handleSort("company_name")),class:"th-company"},[e[69]||(e[69]=E(" 公司名称 ")),t.sortConfig.field==="company_name"?(k(),b("span",gn,g(t.sortConfig.order==="asc"?"▲":"▼"),1)):R("",!0)]),o("th",{onClick:e[13]||(e[13]=s=>t.handleSort("work_location")),class:"th-location"},[e[70]||(e[70]=E(" 工作地点 ")),t.sortConfig.field==="work_location"?(k(),b("span",mn,g(t.sortConfig.order==="asc"?"▲":"▼"),1)):R("",!0)]),o("th",{onClick:e[14]||(e[14]=s=>t.handleSort("salary")),class:"th-salary"},[e[71]||(e[71]=E(" 薪资 ")),t.sortConfig.field==="salary"?(k(),b("span",kn,g(t.sortConfig.order==="asc"?"▲":"▼"),1)):R("",!0)]),o("th",{onClick:e[15]||(e[15]=s=>t.handleSort("education_req")),class:"th-edu"},[e[72]||(e[72]=E(" 学历要求 ")),t.sortConfig.field==="education_req"?(k(),b("span",bn,g(t.sortConfig.order==="asc"?"▲":"▼"),1)):R("",!0)]),o("th",{onClick:e[16]||(e[16]=s=>t.handleSort("major_req")),class:"th-major"},[e[73]||(e[73]=E(" 专业要求 ")),t.sortConfig.field==="major_req"?(k(),b("span",xn,g(t.sortConfig.order==="asc"?"▲":"▼"),1)):R("",!0)]),o("th",{onClick:e[17]||(e[17]=s=>t.handleSort("position_type")),class:"th-type"},[e[74]||(e[74]=E(" 职位类型 ")),t.sortConfig.field==="position_type"?(k(),b("span",wn,g(t.sortConfig.order==="asc"?"▲":"▼"),1)):R("",!0)]),o("th",{onClick:e[18]||(e[18]=s=>t.handleSort("work_type")),class:"th-type"},[e[75]||(e[75]=E(" 工作类型 ")),t.sortConfig.field==="work_type"?(k(),b("span",yn,g(t.sortConfig.order==="asc"?"▲":"▼"),1)):R("",!0)]),o("th",{onClick:e[19]||(e[19]=s=>t.handleSort("post_date")),class:"th-date"},[e[76]||(e[76]=E(" 发布时间 ")),t.sortConfig.field==="post_date"?(k(),b("span",vn,g(t.sortConfig.order==="asc"?"▲":"▼"),1)):R("",!0)]),o("th",{onClick:e[20]||(e[20]=s=>t.handleSort("end_date")),class:"th-date"},[e[77]||(e[77]=E(" 截止时间 ")),t.sortConfig.field==="end_date"?(k(),b("span",_n,g(t.sortConfig.order==="asc"?"▲":"▼"),1)):R("",!0)]),e[78]||(e[78]=o("th",{class:"th-actions"},"操作",-1))])]),o("tbody",null,[(k(!0),b(ue,null,he(t.jobListData.jobs,s=>(k(),b("tr",{key:s.id},[o("td",null,g(s.id),1),o("td",Sn,g(s.position_name||"-"),1),o("td",Cn,g(s.company_name||"-"),1),o("td",Rn,g(s.work_location||"-"),1),o("td",Tn,g(s.salary||"面议"),1),o("td",zn,g(s.education_req||"-"),1),o("td",Ln,g(s.major_req||"-"),1),o("td",Pn,g(s.position_type||"-"),1),o("td",An,g(s.work_type||"-"),1),o("td",null,g(s.post_date||"-"),1),o("td",null,g(t.formatDate(s.end_date)),1),o("td",null,[o("div",En,[o("a",{href:s.position_link,target:"_blank",class:"link-btn"},"详情地址",8,qn),o("button",{class:"expand-btn",onClick:r=>t.showDetails(s)},"详情",8,Dn)])])]))),128))])])):(k(),b("div",Mn," 未找到符合条件的岗位，请尝试其他搜索条件 "))]),t.jobListData.total_pages>1?(k(),b("div",In,[o("a",{class:fe(["page-link",{disabled:t.pagination.currentPage===1}]),onClick:e[21]||(e[21]=s=>t.handlePageChange(t.pagination.currentPage-1))},"上一页",2),(k(!0),b(ue,null,he(t.paginationPages,s=>(k(),b("a",{key:s,class:fe(["page-link",{active:t.pagination.currentPage===s}]),onClick:r=>t.handlePageChange(s)},g(s),11,Fn))),128)),o("a",{class:fe(["page-link",{disabled:t.pagination.currentPage===t.jobListData.total_pages}]),onClick:e[22]||(e[22]=s=>t.handlePageChange(t.pagination.currentPage+1))},"下一页",2)])):R("",!0)]),t.selectedJob?(k(),b("div",{key:0,class:"details-modal",onClick:e[25]||(e[25]=te((...s)=>t.closeDetails&&t.closeDetails(...s),["self"]))},[o("div",Bn,[o("button",{class:"close-btn",onClick:e[23]||(e[23]=(...s)=>t.closeDetails&&t.closeDetails(...s))},"×"),o("div",jn,[o("h3",Nn,g(t.selectedJob.position_name),1),o("div",Un,[o("div",Jn,g(t.selectedJob.company_name),1),o("div",Vn,g(t.selectedJob.salary||"面议"),1)]),o("div",Zn,[o("div",On,g(t.selectedJob.work_location||"地点未知"),1),o("div",Gn,g(t.selectedJob.education_req||"不限学历"),1),o("div",Hn,g(t.selectedJob.work_type||"全职"),1)])]),o("div",Qn,[o("div",Yn,[e[79]||(e[79]=o("h4",null,"职位描述",-1)),o("div",{class:"markdown-content",innerHTML:t.renderMarkdown(t.selectedJob.position_desc||"暂无详细描述")},null,8,Xn)]),o("div",Wn,[e[80]||(e[80]=o("h4",null,"岗位要求",-1)),o("div",{class:"markdown-content",innerHTML:t.renderMarkdown(t.selectedJob.position_requirement||"暂无详细要求")},null,8,Kn)]),o("div",$n,[o("div",es,[e[81]||(e[81]=o("div",{class:"details-label"},"公司类型",-1)),o("div",ts,g(t.selectedJob.company_type||"-"),1)]),o("div",ns,[e[82]||(e[82]=o("div",{class:"details-label"},"所属行业",-1)),o("div",ss,g(t.selectedJob.industry||"-"),1)]),o("div",os,[e[83]||(e[83]=o("div",{class:"details-label"},"职位类型",-1)),o("div",is,g(t.selectedJob.job_type||"-"),1)]),o("div",ls,[e[84]||(e[84]=o("div",{class:"details-label"},"专业要求",-1)),o("div",rs,g(t.selectedJob.major_req||"-"),1)]),o("div",as,[e[85]||(e[85]=o("div",{class:"details-label"},"发布时间",-1)),o("div",cs,g(t.selectedJob.post_date||"-"),1)]),o("div",ds,[e[86]||(e[86]=o("div",{class:"details-label"},"招聘状态",-1)),o("div",ps,g(t.formatDate(t.selectedJob.end_date)),1)])]),o("div",us,[o("a",{href:t.selectedJob.position_link,target:"_blank",class:"apply-btn"},"岗位详情",8,hs),t.selectedJob.backup_link?(k(),b("a",{key:0,href:t.selectedJob.backup_link,target:"_blank",class:"backup-link"},"备用链接",8,fs)):R("",!0),o("button",{class:"edit-btn",onClick:e[24]||(e[24]=s=>t.openEditJob(t.selectedJob))},"编辑岗位")])])])])):R("",!0),t.showEditModal?(k(),b("div",{key:1,class:"edit-modal",onClick:e[46]||(e[46]=te((...s)=>t.closeEditModal&&t.closeEditModal(...s),["self"]))},[o("div",gs,[o("button",{class:"close-btn",onClick:e[26]||(e[26]=(...s)=>t.closeEditModal&&t.closeEditModal(...s))},"×"),e[107]||(e[107]=o("div",{class:"edit-modal-header"},[o("h3",null,"编辑岗位信息")],-1)),o("div",ms,[o("div",ks,[o("div",bs,[e[87]||(e[87]=o("label",null,"公司名称",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[27]||(e[27]=s=>t.editForm.company_name=s),placeholder:"公司名称"},null,512),[[C,t.editForm.company_name]])]),o("div",xs,[e[88]||(e[88]=o("label",null,"公司类型",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[28]||(e[28]=s=>t.editForm.company_type=s),placeholder:"公司类型"},null,512),[[C,t.editForm.company_type]])]),o("div",ws,[e[89]||(e[89]=o("label",null,"行业",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[29]||(e[29]=s=>t.editForm.industry=s),placeholder:"行业"},null,512),[[C,t.editForm.industry]])]),o("div",ys,[e[90]||(e[90]=o("label",null,"岗位名称",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[30]||(e[30]=s=>t.editForm.position_name=s),placeholder:"岗位名称"},null,512),[[C,t.editForm.position_name]])]),o("div",vs,[e[91]||(e[91]=o("label",null,"工作地点",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[31]||(e[31]=s=>t.editForm.work_location=s),placeholder:"工作地点"},null,512),[[C,t.editForm.work_location]])]),o("div",_s,[e[93]||(e[93]=o("label",null,"学历要求",-1)),_(o("select",{"onUpdate:modelValue":e[32]||(e[32]=s=>t.editForm.education_req=s)},e[92]||(e[92]=[Te('<option value="" data-v-7f5e8fa6>不限</option><option value="大专" data-v-7f5e8fa6>大专</option><option value="本科" data-v-7f5e8fa6>本科</option><option value="硕士" data-v-7f5e8fa6>硕士</option><option value="博士" data-v-7f5e8fa6>博士</option>',5)]),512),[[Re,t.editForm.education_req]])]),o("div",Ss,[e[94]||(e[94]=o("label",null,"专业要求",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[33]||(e[33]=s=>t.editForm.major_req=s),placeholder:"专业要求"},null,512),[[C,t.editForm.major_req]])]),o("div",Cs,[e[95]||(e[95]=o("label",null,"薪资",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[34]||(e[34]=s=>t.editForm.salary=s),placeholder:"薪资"},null,512),[[C,t.editForm.salary]])]),o("div",Rs,[e[96]||(e[96]=o("label",null,"招聘类型",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[35]||(e[35]=s=>t.editForm.job_type=s),placeholder:"招聘类型"},null,512),[[C,t.editForm.job_type]])]),o("div",Ts,[e[97]||(e[97]=o("label",null,"岗位类型",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[36]||(e[36]=s=>t.editForm.position_type=s),placeholder:"岗位类型"},null,512),[[C,t.editForm.position_type]])]),o("div",zs,[e[98]||(e[98]=o("label",null,"工作类型",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[37]||(e[37]=s=>t.editForm.work_type=s),placeholder:"工作类型"},null,512),[[C,t.editForm.work_type]])]),o("div",Ls,[e[99]||(e[99]=o("label",null,"发布时间",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[38]||(e[38]=s=>t.editForm.post_date=s),placeholder:"发布时间 (YYYY-MM-DD)"},null,512),[[C,t.editForm.post_date]])]),o("div",Ps,[e[100]||(e[100]=o("label",null,"截止时间",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[39]||(e[39]=s=>t.editForm.end_date=s),placeholder:"截止时间 (YYYY-MM-DD) 或留空表示长期"},null,512),[[C,t.editForm.end_date]])]),o("div",As,[e[101]||(e[101]=o("label",null,"岗位链接",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[40]||(e[40]=s=>t.editForm.position_link=s),placeholder:"岗位链接"},null,512),[[C,t.editForm.position_link]])]),o("div",Es,[e[102]||(e[102]=o("label",null,"备用链接",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[41]||(e[41]=s=>t.editForm.backup_link=s),placeholder:"备用链接"},null,512),[[C,t.editForm.backup_link]])])]),o("div",qs,[e[103]||(e[103]=o("label",null,"岗位职责",-1)),_(o("textarea",{"onUpdate:modelValue":e[42]||(e[42]=s=>t.editForm.position_desc=s),placeholder:"岗位职责",rows:"6"},null,512),[[C,t.editForm.position_desc]]),e[104]||(e[104]=o("div",{class:"help-text"},"支持Markdown格式",-1))]),o("div",Ds,[e[105]||(e[105]=o("label",null,"岗位要求",-1)),_(o("textarea",{"onUpdate:modelValue":e[43]||(e[43]=s=>t.editForm.position_requirement=s),placeholder:"岗位要求",rows:"6"},null,512),[[C,t.editForm.position_requirement]]),e[106]||(e[106]=o("div",{class:"help-text"},"支持Markdown格式",-1))]),o("div",Ms,[o("button",{class:"cancel-btn",onClick:e[44]||(e[44]=(...s)=>t.closeEditModal&&t.closeEditModal(...s))},"取消"),o("button",{class:"save-btn",onClick:e[45]||(e[45]=(...s)=>t.saveJob&&t.saveJob(...s)),disabled:t.isSaving},g(t.isSaving?"保存中...":"保存"),9,Is)])])])])):R("",!0),t.showDomainModal?(k(),b("div",{key:2,class:"domain-modal",onClick:e[48]||(e[48]=te((...s)=>t.closeDomainModal&&t.closeDomainModal(...s),["self"]))},[o("div",Fs,[o("button",{class:"close-btn",onClick:e[47]||(e[47]=(...s)=>t.closeDomainModal&&t.closeDomainModal(...s))},"×"),e[109]||(e[109]=o("div",{class:"domain-modal-header"},[o("h3",null,"批量修改公司名称")],-1)),o("div",Bs,[t.domainLoading?(k(),b("div",js,e[108]||(e[108]=[o("div",{class:"spinner"},null,-1),o("p",null,"加载中...",-1)]))):t.domainList.length===0?(k(),b("div",Ns," 暂无可用域名数据 ")):(k(),b("div",Us,[(k(!0),b(ue,null,he(t.domainList,(s,r)=>(k(),b("div",{class:"domain-item",key:r},[o("div",Js,g(s),1),o("button",{class:"edit-name-btn",onClick:d=>t.openNameEditor(s)},"修改",8,Vs)]))),128))]))])])])):R("",!0),t.showNameEditor?(k(),b("div",{key:3,class:"name-editor-modal",onClick:e[53]||(e[53]=te((...s)=>t.closeNameEditor&&t.closeNameEditor(...s),["self"]))},[o("div",Zs,[o("button",{class:"close-btn",onClick:e[49]||(e[49]=(...s)=>t.closeNameEditor&&t.closeNameEditor(...s))},"×"),e[112]||(e[112]=o("div",{class:"name-editor-header"},[o("h3",null,"修改公司名称")],-1)),o("div",Os,[o("div",Gs,[o("div",Hs,[e[110]||(e[110]=o("span",{class:"info-label"},"当前域名:",-1)),o("span",Qs,g(t.currentDomain),1)])]),o("div",Ys,[o("div",Xs,[e[111]||(e[111]=o("label",null,"新公司名称",-1)),_(o("input",{type:"text","onUpdate:modelValue":e[50]||(e[50]=s=>t.newCompanyName=s),placeholder:"请输入新的公司名称"},null,512),[[C,t.newCompanyName]])]),o("div",Ws,[o("button",{class:"cancel-btn",onClick:e[51]||(e[51]=(...s)=>t.closeNameEditor&&t.closeNameEditor(...s))},"取消"),o("button",{class:"save-btn",onClick:e[52]||(e[52]=(...s)=>t.updateCompanyName&&t.updateCompanyName(...s)),disabled:t.isUpdating},g(t.isUpdating?"更新中...":"更新"),9,Ks)])])])])])):R("",!0),t.loading?(k(),b("div",$s,e[113]||(e[113]=[o("div",{class:"spinner"},null,-1)]))):R("",!0)])}const oo=tt(Ut,[["render",eo],["__scopeId","data-v-7f5e8fa6"]]);export{oo as default};
