import pandas as pd
import pymysql
import concurrent.futures

# 加载Excel文件
df = pd.read_excel(r'D:\OneDrive\桌面\a.xlsx', engine='openpyxl', usecols=["案件编号", "案件名称", "核验项"])

# 筛选出"联系电话"相关的行
filtered_data = df[df['核验项'].str.strip().str.lower() == '联系电话']

# 获取所有案件编号
all_case_numbers = filtered_data['案件编号']

print("共"+str(len(all_case_numbers))+"条电话号码需要修改"+"\n")
print("不满足修改条件：")
for i in all_case_numbers.values:
    if len(i) != 22:
        print(i[0:9], end=' ')
        print(i[9:10], end=' ')
        print(i[10:18], end=' ')
        # if len(i[18:22]) == 3:
        #     print("0"+i[18:22])
        # else:
        print(i[18:22])

# 数据库更新函数
def update_phone_number(case_no):
    # 设置新的联系电话号码
    new_link_num = '15300000000'

    # 连接数据库
    connect = pymysql.connect(host="localhost", user="root", password="123456", database="test", port=3306,
                              charset="utf8")
    cursor = connect.cursor()

    try:
        # 查询更新前的信息
        cursor.execute(f"""
        SELECT l.PERSON_NAME, l.LINK_NUM, l.ORG_NAME
        FROM litigant l
        JOIN case_source cs ON l.id = cs.litigant_id
        JOIN t_push_case_info_task t ON cs.id = t.case_id
        WHERE t.case_no = '{case_no}';
        """)
        result_before = cursor.fetchall()

        # 更新联系电话
        cursor.execute(f"""
        UPDATE litigant l
        JOIN case_source cs ON l.id = cs.litigant_id
        JOIN t_push_case_info_task t ON cs.id = t.case_id
        SET l.LINK_NUM = '{new_link_num}'
        WHERE t.case_no = '{case_no}';
        """)

        # 查询更新后的信息
        cursor.execute(f"""
        SELECT l.PERSON_NAME, l.LINK_NUM, l.ORG_NAME
        FROM litigant l
        JOIN case_source cs ON l.id = cs.litigant_id
        JOIN t_push_case_info_task t ON cs.id = t.case_id
        WHERE t.case_no = '{case_no}';
        """)

        result_after = cursor.fetchall()

        # 提交事务
        connect.commit()

        return result_before, result_after

    except Exception as e:
        print(f"Error with case_no {case_no}: {e}")
        connect.rollback()

    finally:
        cursor.close()
        connect.close()

# 验证更新后的新号码
def validate_new_number(new_link_num):
    connect = pymysql.connect(host="localhost", user="root", password="123456", database="test", port=3306,
                              charset="utf8")
    cursor = connect.cursor()

    try:
        cursor.execute(f"""
        SELECT * FROM litigant WHERE LINK_NUM = '{new_link_num}';
        """)
        validation_result = cursor.fetchall()
        return validation_result

    finally:
        cursor.close()
        connect.close()

# 主函数
def main():
    new_link_num = '15300000000'

    # 使用 ThreadPoolExecutor 进行多线程处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        # 提交所有案件编号的任务
        futures = [executor.submit(update_phone_number, case_no) for case_no in all_case_numbers]

        # 等待所有任务完成
        for future in concurrent.futures.as_completed(futures):
            try:
                result_before, result_after = future.result()
                print(f"Before: {result_before}, After: {result_after}")
            except Exception as e:
                print(f"Error during update: {e}")

    # 验证新号码的记录
    validation_result = validate_new_number(new_link_num)
    print(f"Validation for new number {new_link_num}:")
    for row in validation_result:
        print(row)

# 启动主函数
if __name__ == "__main__":
    main()
    pass