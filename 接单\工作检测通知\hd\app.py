from flask import Flask
import logging
import threading
from flask_cors import CORS
from controller.web_controller import web
from controller.crawler_controller import crawler, scheduled_task


logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)


app = Flask(__name__)

CORS(app, resources={r"/*": {"origins": "*", "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allow_headers": "*"}})

app.register_blueprint(web)
app.register_blueprint(crawler)

if __name__ == '__main__':
    try:
        logging.info("启动自动补全任务线程")
        task_thread = threading.Thread(target=scheduled_task)
        task_thread.daemon = True
        task_thread.start()
        logging.info("自动补全任务线程已启动")
    except Exception as e:
        logging.error(f"启动自动补全任务线程失败: {str(e)}")
    
    app.run(debug=True, host='0.0.0.0', port=5000) 