#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel导入功能
创建一个示例Excel文件用于测试
"""

import pandas as pd
from pathlib import Path

def create_test_excel():
    """创建测试用的Excel文件"""
    
    # 测试数据
    test_data = [
        {
            '公司名称': '腾讯科技',
            '公司类型': '互联网',
            '行业': '计算机软件',
            '岗位名称': 'Python开发工程师',
            '工作地点': '深圳',
            '学历要求': '本科',
            '专业要求': '计算机相关专业',
            '发布时间': '2024-12-01',
            '截止时间': '2024-12-31',
            '岗位链接': 'https://careers.tencent.com/job1',
            '岗位职责': '负责后端开发，维护系统稳定性',
            '岗位要求': '熟悉Python，有3年以上开发经验',
            '备用链接': 'https://backup.tencent.com/job1'
        },
        {
            '公司名称': '阿里巴巴',
            '公司类型': '互联网',
            '行业': '电子商务',
            '岗位名称': 'Java开发工程师',
            '工作地点': '杭州',
            '学历要求': '硕士',
            '专业要求': '计算机科学与技术',
            '发布时间': '2024-12-02',
            '截止时间': '2024-12-30',
            '岗位链接': 'https://careers.alibaba.com/job2',
            '岗位职责': '负责核心业务系统开发',
            '岗位要求': '精通Java，熟悉Spring框架',
            '备用链接': 'https://backup.alibaba.com/job2'
        },
        {
            '公司名称': '百度',
            '公司类型': '互联网',
            '行业': '人工智能',
            '岗位名称': '算法工程师',
            '工作地点': '北京',
            '学历要求': '硕士',
            '专业要求': '人工智能、机器学习',
            '发布时间': '2024-12-03',
            '截止时间': '2024-12-29',
            '岗位链接': 'https://careers.baidu.com/job3',
            '岗位职责': '负责推荐算法优化',
            '岗位要求': '熟悉机器学习算法，有深度学习经验',
            '备用链接': 'https://backup.baidu.com/job3'
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    # 确保下载文件夹存在
    download_folder = Path('downloads')
    download_folder.mkdir(exist_ok=True)
    
    # 创建测试子文件夹
    test_folder = download_folder / 'test_account'
    test_folder.mkdir(exist_ok=True)
    
    # 保存Excel文件
    excel_file = test_folder / '20241201_143052_123_测试招聘数据.xlsx'
    df.to_excel(excel_file, index=False)
    
    print(f"✅ 测试Excel文件已创建: {excel_file}")
    print(f"📊 包含 {len(test_data)} 条测试数据")
    print("\n数据预览:")
    print(df.to_string(index=False))
    
    return excel_file

if __name__ == "__main__":
    try:
        create_test_excel()
        print("\n💡 现在可以使用GUI的'📊 导入Excel到数据库'功能测试导入")
    except Exception as e:
        print(f"❌ 创建测试文件失败: {e}")
