-- 创建数据库
CREATE DATABASE IF NOT EXISTS job_crawler DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE job_crawler;

-- 创建URL表
CREATE TABLE IF NOT EXISTS urls (
    id INT AUTO_INCREMENT PRIMARY KEY,
    url VARCHAR(255) NOT NULL,
    type VARCHAR(100) COMMENT '类型',
    Category INT NOT NULL,
    detail_url VARCHAR(255) COMMENT '详情地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- 创建岗位信息表
CREATE TABLE IF NOT EXISTS jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_name VARCHAR(255) COMMENT '公司名称',
    company_type VARCHAR(100) COMMENT '公司类型',
    industry VARCHAR(100) COMMENT '行业',
    position_name VARCHAR(255) COMMENT '岗位名称',
    position_type VARCHAR(100) COMMENT '岗位类型',
    work_location VARCHAR(255) COMMENT '工作地点',
    education_req VARCHAR(100) COMMENT '学历要求',
    salary VARCHAR(100) COMMENT '薪资',
    job_type VARCHAR(100) COMMENT '招聘类型',
    work_type VARCHAR(100) COMMENT '工作类型',
    major_req VARCHAR(255) COMMENT '专业要求',
    post_date VARCHAR(50) COMMENT '发布时间',
    end_date VARCHAR(50) COMMENT '截止时间',
    position_link VARCHAR(255) COMMENT '岗位链接',
    position_desc TEXT COMMENT '岗位职责',
    position_requirement TEXT COMMENT '岗位要求',
    backup_link VARCHAR(255) COMMENT '备用链接',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_company_position (company_name, position_name, post_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入初始URL数据
INSERT INTO urls (url,type,Category,detail_url) VALUES 
('https://sqzm.zhiye.com','bs',3,'campus/detail'),
('https://lppz.zhiye.com','bs',2,'campus/detail'),
('https://sunwodacampus.zhiye.com','bs',2,'campus/detail'),
('https://xiaoyuan.zhaopin.com/company/KA0220009411D90000021000?refcode=4404&srccode=440401&jobSourceType=2','智联',1,'/job/');