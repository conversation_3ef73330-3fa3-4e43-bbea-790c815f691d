import requests
from pathlib import Path

import time
from functools import wraps

def rate_limited(max_calls, window_seconds):
    def decorator(func):
        calls = []

        @wraps(func)
        def wrapper(*args, **kwargs):
            while True:
                now = time.time()
                # 清理过期记录
                calls[:] = [ts for ts in calls if ts >= now - window_seconds]
                if len(calls) < max_calls:
                    calls.append(now)
                    return func(*args, **kwargs)
                else:
                    # 计算等待时间并阻塞
                    oldest = calls[0]
                    wait = window_seconds - (now - oldest)
                    time.sleep(wait)

        return wrapper

    return decorator

class WecomRobot:
    def __init__(self, webhook_key):
        self._webhook_key = webhook_key
        self._count = 0

    @rate_limited(max_calls=18, window_seconds=60)
    def send_data(self, data:dict):
        url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send"
        headers = {
            'Content-Type': 'application/json'
        }
        params = {
            'key': self._webhook_key,
        }

        response = requests.post(url, headers=headers, params=params, json=data)
        response.raise_for_status()  # 检查HTTP错误
        self._count = self._count + 1
        return response

    def send_message(self, message:str, mentioned_list:list[str]=None):
        if mentioned_list is None:
            mentioned_list = []

        data = {
            "msgtype": "text",
            "text": {
                "content": message,
                "mentioned_list": mentioned_list
            }
        }

        return self.send_data(data)

    def send_markdown(self, message:str, mentioned_list:list[str]=None):
        if mentioned_list is None:
            mentioned_list = []

        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": message,
                "mentioned_list": mentioned_list
            }
        }

        return self.send_data(data)

    def send_file(self, file_id:str):
        data = {
            "msgtype": "file",
            "file": {"media_id": file_id}
        }

        return self.send_data(data)

    def upload_file(self, file_path:Path) -> str:
        url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media"

        headers = {
            'Content-Type': 'multipart/form-data'
        }

        params = {
            'key': self._webhook_key,
            'type': 'file'
        }

        files = {
            'media': (file_path.name, file_path.read_bytes(), 'application/octet-stream')
        }

        response = requests.post(url, headers=headers, params=params, files=files)
        response.raise_for_status()
        return response.json()["media_id"]