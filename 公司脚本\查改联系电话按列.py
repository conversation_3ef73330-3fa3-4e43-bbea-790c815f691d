import pandas as pd
import pymysql

# 加载Excel文件，header=1表示使用第二行作为列标题
df = pd.read_excel(r'D:\OneDrive\桌面\c.xlsx', engine='openpyxl', header=None)

# 读取第1列和第5列的数据
first_and_fifth_columns = df.iloc[:, [1, 4]]

# 获取第一列的数据
first_column = first_and_fifth_columns.iloc[:, 0]
num = first_and_fifth_columns.iloc[:, 1]

bh = [str(i).replace('\'', '') for i in first_column]

# 数据库连接配置
db_config = {
    "host": "localhost",
    "user": "root",
    "password": "123456",
    "database": "test",
    "port": 3306,
    "charset": "utf8"
}

# 数据库更新函数
def update_phone_number(cursor, case_no, new_link_num):
    try:
        # 查询更新前的信息
        cursor.execute("""
        SELECT l.PERSON_NAME, l.LINK_NUM, l.ORG_NAME
        FROM litigant l
        JOIN case_source cs ON l.id = cs.litigant_id
        JOIN t_push_case_info_task t ON cs.id = t.case_id
        WHERE t.case_no = %s;
        """, (case_no,))
        result_before = cursor.fetchall()

        # 更新联系电话
        cursor.execute("""
        UPDATE litigant l
        JOIN case_source cs ON l.id = cs.litigant_id
        JOIN t_push_case_info_task t ON cs.id = t.case_id
        SET l.LINK_NUM = %s
        WHERE t.case_no = %s;
        """, (new_link_num, case_no))

        # 查询更新后的信息
        cursor.execute("""
        SELECT l.PERSON_NAME, l.LINK_NUM, l.ORG_NAME
        FROM litigant l
        JOIN case_source cs ON l.id = cs.litigant_id
        JOIN t_push_case_info_task t ON cs.id = t.case_id
        WHERE t.case_no = %s;
        """, (case_no,))
        result_after = cursor.fetchall()

        return result_before, result_after

    except Exception as e:
        print(f"Error with case_no {case_no}: {e}")
        raise


# 验证更新后的新号码
def validate_new_number(cursor, new_link_num):
    cursor.execute("""
    SELECT * FROM litigant WHERE LINK_NUM = %s;
    """, (new_link_num,))
    validation_result = cursor.fetchall()
    return validation_result


if __name__ == '__main__':
    # 使用数据库连接池
    with pymysql.connect(**db_config) as connection:
        with connection.cursor() as cursor:
            for i in range(len(bh)):
                try:
                    result_before, result_after = update_phone_number(cursor, bh[i], num[i])
                    validation_result = validate_new_number(cursor, num[i])

                    # 打印结果
                    print(f"Before Update: {result_before}")
                    print(f"After Update: {result_after}")
                    print(f"Validation: {validation_result}")
                    print()

                    # 提交事务
                    connection.commit()
                except Exception as e:
                    # 出现异常时回滚事务
                    connection.rollback()
                    print(f"Transaction failed for case_no {bh[i]}: {e}")
