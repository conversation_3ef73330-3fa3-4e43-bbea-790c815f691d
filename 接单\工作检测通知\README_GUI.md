# QQ邮箱Excel文件下载器 - 图形化界面版

## 🎨 功能特点

### ✨ 邮件下载功能
- **多账号支持** - 同时管理多个QQ邮箱账号
- **多线程下载** - 并行处理，提高效率
- **智能筛选** - 按时间范围筛选邮件
- **Excel专用** - 只下载Excel格式文件
- **文件管理** - 按邮箱分类存储

### 📊 数据库导入功能
- **Excel解析** - 自动读取下载的Excel文件
- **数据映射** - 智能映射Excel列到数据库字段
- **批量导入** - 一键导入所有Excel文件
- **错误处理** - 自动重试和错误跳过
- **进度显示** - 实时显示导入进度

### 🎯 用户界面
- **现代化设计** - 扁平化风格，美观易用
- **双面板布局** - 账号管理 + 下载控制
- **实时日志** - 详细的操作日志显示
- **进度可视** - 进度条和状态提示
- **一键操作** - 所有功能都是按钮点击

## 🚀 快速开始

### 1. 安装依赖
```bash
# 方法1: 使用安装脚本
python install_requirements.py

# 方法2: 手动安装
pip install imapclient pandas pymysql openpyxl xlrd
```

### 2. 运行程序
```bash
python qq_downloader_gui.py
```

### 3. 配置邮箱
1. 点击 "➕ 添加账号" 按钮
2. 输入QQ邮箱和授权码
3. 点击 "✅ 添加账号"

### 4. 配置数据库（可选）
1. 点击 "🔧 数据库配置" 按钮
2. 输入MySQL数据库连接信息
3. 点击 "🔍 测试连接" 验证
4. 点击 "✅ 保存" 保存配置

### 5. 下载Excel文件
1. 设置搜索天数和线程数
2. 选择下载目录
3. 点击 "🚀 开始下载Excel文件"

### 6. 导入数据库（可选）
1. 确保已配置数据库连接
2. 点击 "📊 导入Excel到数据库"
3. 确认导入操作

## 📋 Excel字段映射

Excel文件的列应按以下顺序排列：

| 列序号 | Excel列名 | 数据库字段 | 说明 |
|--------|-----------|------------|------|
| 1 | 公司名称 | company_name | 招聘公司名称 |
| 2 | 公司类型 | company_type | 公司类型（如：互联网、制造业等） |
| 3 | 行业 | industry | 所属行业 |
| 4 | 岗位名称 | position_name | 职位名称 |
| 5 | 工作地点 | work_location | 工作城市或地区 |
| 6 | 学历要求 | education_req | 最低学历要求 |
| 7 | 专业要求 | major_req | 专业要求 |
| 8 | 发布时间 | post_date | 职位发布时间 |
| 9 | 截止时间 | end_date | 申请截止时间 |
| 10 | 岗位链接 | position_link | 职位详情链接 |
| 11 | 岗位职责 | position_desc | 工作职责描述 |
| 12 | 岗位要求 | position_requirement | 任职要求 |
| 13 | 备用链接 | backup_link | 备用链接 |

## 🔧 获取QQ邮箱授权码

1. 登录 [QQ邮箱网页版](https://mail.qq.com)
2. 点击 **设置** → **账户**
3. 找到 **POP3/IMAP/SMTP服务**
4. 开启 **IMAP/SMTP服务**
5. 生成 **授权码**（不是QQ密码）
6. 将授权码填入程序中

## 📊 数据库配置

### MySQL数据库要求
- MySQL 5.7+ 或 MariaDB 10.2+
- 字符集：utf8mb4
- 需要创建 `job_crawler` 数据库

### 创建数据库
```sql
CREATE DATABASE IF NOT EXISTS job_crawler 
DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 数据表结构
程序会自动使用 `jobs` 表，表结构请参考 `hd/job_crawler.sql` 文件。

## 🎯 使用技巧

### 邮件下载
- **搜索天数**: 建议设置7-30天，避免下载过多历史邮件
- **线程数**: 建议2-5个，过多可能被邮箱服务器限制
- **文件夹**: 选择有足够空间的目录

### Excel导入
- **文件格式**: 支持 .xlsx, .xls, .xlsm, .xlsb 格式
- **数据质量**: 确保Excel文件列顺序正确
- **错误处理**: 程序会自动跳过无法解析的文件

### 性能优化
- **定期清理**: 定期清理下载文件夹
- **数据库维护**: 定期清理重复数据
- **网络环境**: 确保网络连接稳定

## ❓ 常见问题

### Q: 邮箱连接失败？
A: 检查授权码是否正确，确保开启了IMAP服务

### Q: 数据库连接失败？
A: 检查数据库配置，确保MySQL服务正在运行

### Q: Excel文件无法导入？
A: 检查文件格式和列顺序，确保数据完整

### Q: 下载速度慢？
A: 适当增加线程数，检查网络连接

## 📁 文件结构

```
接单/工作检测通知/
├── qq_downloader_gui.py      # 主程序（图形界面版）
├── qq_downloader_simple.py   # 命令行版本
├── install_requirements.py   # 依赖安装脚本
├── test_excel_import.py      # 测试Excel创建脚本
├── README_GUI.md             # 使用说明
├── email_config.json         # 配置文件（自动生成）
├── downloads/                # 下载文件夹（自动创建）
└── hd/
    └── job_crawler.sql       # 数据库表结构
```

## 🎉 版本信息

- **版本**: v1.0.0
- **作者**: Augment Agent
- **更新**: 2024-12-01
- **许可**: MIT License

## 💡 技术支持

如有问题或建议，请检查：
1. 依赖库是否正确安装
2. QQ邮箱授权码是否有效
3. 数据库连接是否正常
4. Excel文件格式是否正确
