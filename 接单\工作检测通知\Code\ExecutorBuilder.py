import json
from typing import List, Dict, Type
from Executor import Executor
from JobExcelFormatter import <PERSON>ExcelFormatter
from JobExcelDownloader import JobExcelDownloader
from JobExcelSender import JobExcelSender
from JobExcelUploader import <PERSON>ExcelUploader, ExcelFieldMapping
from QQExcelDownloader import QQExcelDownloader
from SmartDocument import TencentSmartClient

import logging
logger = logging.getLogger(__name__)

class ExecutorBuilder:
    def __init__(self):
        self._registry: Dict[str, Type[Executor]] = {
            "JobExcelDownloader": JobExcelDownloader,
            "JobExcelFormatter": JobExcelFormatter,
            "JobExcelSender": JobExcelSender,
            "JobExcelUploader": self._create_uploader,
            "QQExcelDownloader": QQExcelDownloader
        }

    def build(self, config:Dict, isDebug:bool) -> List[Executor]:
        """从配置构建执行器列表
        
        Args:
            config: 完整的配置字典
            
        Returns:
            List[Executor]: 执行器列表
        """
        executors = []
        for executor_config in config.get("Executors", []):
            try:
                executor = self._create_executor(executor_config, isDebug)
                executors.append(executor)
            except Exception as e:
                logger.error(f"Failed to create executor: {str(e)}")
                raise
        
        return executors

    def _create_executor(self, config:Dict, isDebug:bool) -> Executor:
        """创建单个执行器（私有方法）"""
        executor_type = config.get("type")
        if not executor_type:
            raise ValueError("Executor config missing 'type' field")

        executor_class = self._registry.get(executor_type)
        if not executor_class:
            raise ValueError(f"Unknown executor type: {executor_type}")

        # 过滤掉type字段
        params = {k: v for k, v in config.items() if k != "type"}
        params["is_debug"] = isDebug

        try:
            if callable(executor_class):
                return executor_class(**params)
            return executor_class(**params)
        except TypeError as e:
            raise ValueError(f"Invalid parameters for {executor_type}: {str(e)}")

    def _create_uploader(self, **config) -> Executor:
        """特殊处理JobExcelUploader的创建"""
        # 创建腾讯文档客户端
        client_config = config.pop("client")
        client = TencentSmartClient(**client_config)

        # 创建字段映射
        field_mappings = [
            ExcelFieldMapping(**mapping)
            for mapping in config.pop("field_mappings")
        ]

        # 创建上传器
        return JobExcelUploader(
            client=client,
            field_mappings=field_mappings,
            **config
        )
