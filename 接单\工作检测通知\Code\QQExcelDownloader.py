import logging
from datetime import datetime, timedelta
from pathlib import Path
import shutil
import re
from Executor import Executor
from email.header import decode_header
import email
from imapclient import IMAPClient

logger = logging.getLogger(__name__)


class QQExcelDownloader(Executor):
    """QQ邮箱Excel文件下载器
    
    专门用于从QQ邮箱下载Excel附件的执行器
    支持按发件人、时间范围、主题关键词等条件筛选邮件
    """
    
    def __init__(self, output_directory_path: str, qq_email_configs: list[dict], 
                 sender_filters: list[str] = None, subject_keywords: list[str] = None,
                 since_days: int = 7, is_debug: bool = False):
        """
        初始化QQ邮箱下载器
        
        Args:
            output_directory_path: 输出目录路径
            qq_email_configs: QQ邮箱配置列表，格式：[{"username": "<EMAIL>", "password": "授权码"}]
            sender_filters: 发件人过滤列表，只下载这些发件人的邮件
            subject_keywords: 主题关键词列表，包含这些关键词的邮件才会被处理
            since_days: 下载多少天内的邮件，默认7天
            is_debug: 是否为调试模式
        """
        self._output_directory = output_directory_path
        self._qq_email_configs = qq_email_configs
        self._sender_filters = sender_filters or []
        self._subject_keywords = subject_keywords or []
        self._since_days = since_days
        self._is_debug = is_debug
        self._imap_server = "imap.qq.com"  # QQ邮箱IMAP服务器
        
    def execute(self):
        """执行下载任务"""
        output_directory = Path(self._output_directory)
        
        # 清理输出目录
        if output_directory.exists():
            shutil.rmtree(output_directory)
        output_directory.mkdir(parents=True, exist_ok=True)
        
        # 遍历所有配置的QQ邮箱
        for email_config in self._qq_email_configs:
            username = email_config["username"]
            password = email_config["password"]
            
            try:
                self._download_from_qq_email(username, password, output_directory)
                logger.info(f"成功处理QQ邮箱: {username}")
            except Exception as e:
                logger.error(f"处理QQ邮箱 {username} 时出错: {str(e)}")
                if self._is_debug:
                    raise
    
    def _download_from_qq_email(self, username: str, password: str, output_directory: Path):
        """从指定QQ邮箱下载Excel文件"""
        with IMAPClient(host=self._imap_server, port=993, ssl=True) as client:
            # QQ邮箱登录
            client.login(username, password)
            
            # 发送ID命令（QQ邮箱要求）
            client.id_({"name": "QQExcelDownloader", "version": "1.0.0"})
            
            # 选择收件箱
            client.select_folder('INBOX')
            
            # 构建搜索条件
            search_criteria = self._build_search_criteria()
            messages = client.search(search_criteria)
            
            logger.info(f"在 {username} 中找到 {len(messages)} 封符合条件的邮件")
            
            processed_count = 0
            for msg_id, msg_data in client.fetch(messages, ['RFC822', 'ENVELOPE']).items():
                try:
                    if self._process_email_message(msg_data, output_directory, username):
                        processed_count += 1
                        
                    # 调试模式下只处理第一封邮件
                    if self._is_debug and processed_count >= 1:
                        break
                        
                except Exception as e:
                    logger.error(f"处理邮件 {msg_id} 时出错: {str(e)}")
                    continue
            
            logger.info(f"从 {username} 成功下载了 {processed_count} 封邮件的Excel附件")
    
    def _build_search_criteria(self) -> list:
        """构建IMAP搜索条件"""
        criteria = []
        
        # 时间范围筛选
        since_date = (datetime.now() - timedelta(days=self._since_days)).strftime("%d-%b-%Y")
        criteria.extend(['SINCE', since_date])
        
        # 发件人筛选
        if self._sender_filters:
            # 如果有多个发件人，使用OR条件
            if len(self._sender_filters) == 1:
                criteria.extend(['FROM', self._sender_filters[0]])
            else:
                # 构建复杂的OR查询
                from_criteria = []
                for sender in self._sender_filters:
                    from_criteria.extend(['FROM', sender])
                criteria.extend(['OR'] + from_criteria)
        
        # 只搜索有附件的邮件
        criteria.append('HAS ATTACHMENT')
        
        return criteria
    
    def _process_email_message(self, msg_data: dict, output_directory: Path, username: str) -> bool:
        """处理单个邮件消息
        
        Returns:
            bool: 是否成功处理了邮件
        """
        raw_email = msg_data[b'RFC822']
        parsed_email = email.message_from_bytes(raw_email)
        
        # 获取邮件基本信息
        sender = parsed_email.get('From', '')
        subject = self._decode_header_value(parsed_email.get('Subject', ''))
        date_str = parsed_email.get('Date', '')
        
        # 验证发件人
        if self._sender_filters and not any(filter_sender in sender for filter_sender in self._sender_filters):
            return False
        
        # 验证主题关键词
        if self._subject_keywords and not any(keyword in subject for keyword in self._subject_keywords):
            return False
        
        # 解析邮件日期
        try:
            email_date = email.utils.parsedate_to_datetime(date_str)
            if email_date < (datetime.now(email_date.tzinfo) - timedelta(days=self._since_days)):
                return False
        except Exception as e:
            logger.warning(f"无法解析邮件日期: {date_str}, 错误: {str(e)}")
            email_date = datetime.now()
        
        # 提取发件人名称或邮箱作为分类
        sender_name = self._extract_sender_name(sender)
        formatted_date = email_date.strftime("%Y%m%d_%H%M%S")
        
        # 创建目标目录
        target_dir = output_directory / sender_name
        target_dir.mkdir(parents=True, exist_ok=True)
        
        # 处理附件
        excel_count = 0
        for part in parsed_email.walk():
            if part.get_content_maintype() == 'multipart':
                continue
            
            content_disposition = part.get('Content-Disposition')
            if content_disposition is None:
                continue
            
            filename = part.get_filename()
            if not filename:
                continue
            
            # 解码文件名
            decoded_filename = self._decode_filename(filename)
            
            # 只处理Excel文件
            if not self._is_excel_file(decoded_filename):
                continue
            
            # 生成保存文件名
            file_extension = Path(decoded_filename).suffix
            safe_subject = self._make_safe_filename(subject)
            save_filename = f"{formatted_date}_{safe_subject}_{excel_count + 1}{file_extension}"
            save_path = target_dir / save_filename
            
            # 保存附件
            try:
                with open(save_path, 'wb') as f:
                    f.write(part.get_payload(decode=True))
                excel_count += 1
                logger.info(f"保存Excel文件: {save_path}")
            except Exception as e:
                logger.error(f"保存文件 {save_filename} 时出错: {str(e)}")
        
        return excel_count > 0
    
    def _decode_header_value(self, header_value: str) -> str:
        """解码邮件头部值"""
        if not header_value:
            return ""
        
        try:
            decoded_parts = decode_header(header_value)
            result = ""
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    result += part.decode(encoding or 'utf-8', errors='ignore')
                else:
                    result += part
            return result
        except Exception:
            return header_value
    
    def _decode_filename(self, encoded_name: str) -> str:
        """解码文件名"""
        if not encoded_name:
            return ""
        
        try:
            decoded = decode_header(encoded_name)[0]
            if isinstance(decoded[0], bytes):
                # QQ邮箱常用编码：UTF-8, GBK, GB2312
                encodings = [decoded[1], 'utf-8', 'gbk', 'gb2312', 'latin-1']
                for encoding in encodings:
                    if encoding:
                        try:
                            return decoded[0].decode(encoding)
                        except (UnicodeDecodeError, LookupError):
                            continue
                # 如果所有编码都失败，使用错误处理
                return decoded[0].decode('utf-8', errors='ignore')
            return decoded[0]
        except Exception:
            return encoded_name
    
    def _extract_sender_name(self, sender: str) -> str:
        """从发件人字段提取名称"""
        # 尝试提取邮箱地址
        email_match = re.search(r'<(.+?)>', sender)
        if email_match:
            email_addr = email_match.group(1)
            # 提取邮箱用户名部分
            username = email_addr.split('@')[0]
            return username
        
        # 如果没有尖括号，直接使用发件人字段
        clean_sender = re.sub(r'[<>"]', '', sender)
        if '@' in clean_sender:
            return clean_sender.split('@')[0]
        
        return self._make_safe_filename(clean_sender)
    
    def _is_excel_file(self, filename: str) -> bool:
        """判断是否为Excel文件"""
        excel_extensions = ['.xlsx', '.xls', '.xlsm', '.xlsb']
        return any(filename.lower().endswith(ext) for ext in excel_extensions)
    
    def _make_safe_filename(self, filename: str) -> str:
        """生成安全的文件名（移除非法字符）"""
        # 移除Windows文件系统不允许的字符
        illegal_chars = r'[<>:"/\\|?*]'
        safe_name = re.sub(illegal_chars, '_', filename)
        
        # 限制长度
        if len(safe_name) > 50:
            safe_name = safe_name[:50]
        
        # 移除首尾空格和点号
        safe_name = safe_name.strip('. ')
        
        return safe_name or "unnamed"
