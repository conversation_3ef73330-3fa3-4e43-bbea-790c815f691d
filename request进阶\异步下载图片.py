import aiohttp
import asyncio

url = {
    "https://zyxcsdy.oss-cn-wuhan-lr.aliyuncs.com/previewr.jpg",
    "https://zyxcsdy.oss-cn-wuhan-lr.aliyuncs.com/previewl.jpg",
    "https://zyxcsdy.oss-cn-wuhan-lr.aliyuncs.com/Screenshot_2024_0714_211902.png",
    "https://zyxcsdy.oss-cn-wuhan-lr.aliyuncs.com/Screenshot_2024_0714_211607.png"
}


async def download_image(url):
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            with open(url.split("/")[-1], "wb") as f:
                f.write(await response.content.read())


async def main():
    tasks = []
    for i in url:
        tasks.append(download_image(i))
    await asyncio.gather(*tasks)


if __name__ == '__main__':
    asyncio.run(main())
