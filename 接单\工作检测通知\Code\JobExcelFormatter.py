import logging
from Executor import Executor
from openpyxl import load_workbook
from pathlib import Path
import xlsxwriter
from xlsxwriter.format import Format

import shutil

logger = logging.getLogger(__name__)

class JobExcelFormatter(Executor):
    def __init__(self, input_directory_path:str, output_directory_path:str, is_debug:bool, format_config:dict=None):
        """
        初始化 JobExcelFormatter 对象
        
        Args:
            input_directory_path: 输入目录路径
            output_directory_path: 输出目录路径
            is_debug: 是否为调试模式
            format_config: 格式配置字典，可包含以下键：
                - row_formats: 行格式配置，格式为 {行索引: 格式字典}
                - column_formats: 列格式配置，格式为 {列索引: 格式字典}
                - cell_formats: 单元格格式配置，格式为 {"行_列": 格式字典}
                - default_row_height: 默认行高
                - default_column_widths: 默认列宽配置，格式为 {"列范围": 宽度}
                - default_cell_format: 默认单元格格式
        """
        self._inputDirectory = input_directory_path
        self._outputDirectory = output_directory_path
        
        # 默认格式配置
        self._format_config = {
            # 行格式配置，键为行索引（从1开始），值为格式字典
            'row_formats': {},  
            
            # 列格式配置，键为列索引（从1开始），值为格式字典
            'column_formats': {},  
            
            # 特定单元格格式配置，键为"行_列"格式的字符串，值为格式字典
            'cell_formats': {},  
            
            # 默认行高（单位：磅）
            'default_row_height': 50,  
            
            # 默认列宽配置，键为列范围（如"A-J"），值为列宽
            'default_column_widths': {  
                'A-J': 16,  # A-J列宽为16
                'K-M': 50   # K-M列宽为50
            },
            
            # 默认单元格格式，包含字体、颜色、对齐等属性
            'default_cell_format': {  
                'font_name': '微软雅黑',  # 字体名称
                'font_size': 12,         # 字体大小
                'bold': False,           # 是否加粗
                'italic': False,         # 是否斜体
                'font_color': 'black',   # 字体颜色
                'bg_color': 'white',     # 背景颜色
                'alignment': 'center',     # 水平对齐方式：left, center, right, justify
                'vertical_alignment': 'vcenter',  # 垂直对齐方式：top, vcenter, bottom
                'text_wrap': True,       # 是否自动换行
                'border': 1,             # 边框样式：0-7（0:无边框,1:细边框,2:中等边框,等）
                'border_color': 'black', # 边框颜色
                'left': 1,               # 左边框样式
                'right': 1,              # 右边框样式
                'top': 1,                # 上边框样式
                'bottom': 1,             # 下边框样式
                'left_color': None,      # 左边框颜色（None表示使用border_color）
                'right_color': None,     # 右边框颜色
                'top_color': None,       # 上边框颜色
                'bottom_color': None     # 下边框颜色
            }
        }
        
        # 从传入的配置更新默认配置
        if format_config:
            # 转换字符串键为整数键（用于行和列格式）
            if 'row_formats' in format_config:
                self._format_config['row_formats'] = {
                    int(k): v for k, v in format_config['row_formats'].items()
                }
            if 'column_formats' in format_config:
                self._format_config['column_formats'] = {
                    int(k): v for k, v in format_config['column_formats'].items()
                }
            if 'cell_formats' in format_config:
                self._format_config['cell_formats'] = format_config['cell_formats']
            if 'default_row_height' in format_config:
                self._format_config['default_row_height'] = format_config['default_row_height']
            if 'default_column_widths' in format_config:
                self._format_config['default_column_widths'] = format_config['default_column_widths']
            if 'default_cell_format' in format_config:
                self._format_config['default_cell_format'] = format_config['default_cell_format']

    def _apply_format(self, workbook:xlsxwriter.Workbook, row, col, cell_format:Format):
        """
        应用格式配置到单元格格式对象，按优先级顺序：
        特定单元格格式 > 列格式 > 行格式 > 默认单元格格式
        
        Args:
            workbook: xlsxwriter工作簿对象
            row: 行索引（从0开始）
            col: 列索引（从0开始）
            cell_format: 基础单元格格式对象
            
        Returns:
            更新后的单元格格式对象
        """
        # 将0基索引转换为1基索引，因为配置中使用的是从1开始的索引
        row_idx = row + 1
        col_idx = col + 1
        
        # 应用默认单元格格式
        if 'default_cell_format' in self._format_config:
            self._update_format(cell_format, self._format_config['default_cell_format'])
            
        # 应用行格式
        if row_idx in self._format_config['row_formats']:
            row_format = self._format_config['row_formats'][row_idx]
            self._update_format(cell_format, row_format)

        # 应用列格式
        if col_idx in self._format_config['column_formats']:
            col_format = self._format_config['column_formats'][col_idx]
            self._update_format(cell_format, col_format)

        # 应用特定单元格格式
        cell_key = f"{row_idx}_{col_idx}"
        if cell_key in self._format_config['cell_formats']:
            cell_format_dict = self._format_config['cell_formats'][cell_key]
            self._update_format(cell_format, cell_format_dict)

        return cell_format

    def _update_format(self, format_obj:Format, format_dict:dict):
        """
        更新格式对象的属性
        
        Args:
            format_obj: xlsxwriter格式对象
            format_dict: 格式配置字典，可包含以下键：
                - font_name: 字体名称
                - font_size: 字体大小
                - bold: 是否加粗
                - italic: 是否斜体
                - font_color: 字体颜色
                - bg_color: 背景颜色
                - alignment: 对齐方式
                - vertical_alignment: 垂直对齐方式
                - text_wrap: 是否自动换行
                - border: 边框样式
                - border_color: 边框颜色
                - left/right/top/bottom: 各边边框样式
                - left_color/right_color/top_color/bottom_color: 各边边框颜色
        """
        if 'font_name' in format_dict:
            format_obj.set_font_name(format_dict['font_name'])
        if 'font_size' in format_dict:
            format_obj.set_font_size(format_dict['font_size'])
        if 'bold' in format_dict:
            format_obj.set_bold(format_dict['bold'])
        if 'italic' in format_dict:
            format_obj.set_italic(format_dict['italic'])
        if 'font_color' in format_dict:
            format_obj.set_font_color(format_dict['font_color'])
        if 'bg_color' in format_dict:
            format_obj.set_bg_color(format_dict['bg_color'])
        if 'alignment' in format_dict:
            format_obj.set_align(format_dict['alignment'])
        if 'vertical_alignment' in format_dict:
            format_obj.set_align(format_dict['vertical_alignment'])
        if 'text_wrap' in format_dict and format_dict['text_wrap']:
            format_obj.set_text_wrap()
            
        # 应用边框设置
        if 'border' in format_dict:
            format_obj.set_border(format_dict['border'])
        if 'border_color' in format_dict:
            format_obj.set_border_color(format_dict['border_color'])
            
        # 应用各个边的边框设置
        if 'left' in format_dict:
            format_obj.set_left(format_dict['left'])
        if 'right' in format_dict:
            format_obj.set_right(format_dict['right'])
        if 'top' in format_dict:
            format_obj.set_top(format_dict['top'])
        if 'bottom' in format_dict:
            format_obj.set_bottom(format_dict['bottom'])
            
        # 应用各个边的边框颜色
        if 'left_color' in format_dict and format_dict['left_color']:
            format_obj.set_left_color(format_dict['left_color'])
        if 'right_color' in format_dict and format_dict['right_color']:
            format_obj.set_right_color(format_dict['right_color'])
        if 'top_color' in format_dict and format_dict['top_color']:
            format_obj.set_top_color(format_dict['top_color'])
        if 'bottom_color' in format_dict and format_dict['bottom_color']:
            format_obj.set_bottom_color(format_dict['bottom_color'])

    def execute(self):
        inputDirectory = Path(self._inputDirectory)
        outputDirectory = Path(self._outputDirectory)
        # 递归删除目录下所有文件
        if outputDirectory.exists():
            shutil.rmtree(outputDirectory)

        outputDirectory.mkdir(parents=True, exist_ok=True)

        # 递归遍历文件夹中的所有文件夹
        for item in inputDirectory.iterdir():
            if item.is_dir():  # 判断是否为文件夹
                self._handle_directory(item, outputDirectory)


    def _handle_directory(self, sourceDirectory, targetParentDirectory):
        print(f'文件夹：{sourceDirectory}')

        for sourcePath in sourceDirectory.iterdir():
            if sourcePath.name.endswith('.xlsx'):
                # 构建完整文件路径
                sourcePath = sourcePath
                targetDirectory = targetParentDirectory.joinpath(sourceDirectory.name)
                targetDirectory.mkdir(parents=True, exist_ok=True)
                targetPath = targetDirectory.joinpath(sourcePath.name)

                # 读取源文件和模板文件
                sourceWorkbook = load_workbook(sourcePath)
                 
                # 创建新的工作簿
                targetWorkbook = xlsxwriter.Workbook(targetPath)
                
                # 复制每个工作表
                for i in range(len(sourceWorkbook.worksheets)):
                    sourceSheet = sourceWorkbook.worksheets[i]
                    targetSheet = targetWorkbook.add_worksheet(self._get_sheet_name(i, sourceSheet.title))
                    
                    # 设置列宽
                    for col_range, width in self._format_config['default_column_widths'].items():
                        start_col, end_col = col_range.split('-')
                        start_idx = ord(start_col) - ord('A')
                        end_idx = ord(end_col) - ord('A')
                        for col in range(start_idx, end_idx + 1):
                            targetSheet.set_column(col, col, width)
                    
                    # 复制单元格内容
                    for row in sourceSheet.iter_rows():
                        for cell in row:
                            if cell.value is not None:
                                rowIndex = cell.row - 2
                                colIndex = cell.column - 1
                                # 复制超链接
                                if cell.hyperlink:
                                    # 获取或创建包含超链接的格式
                                    cell_format = self._create_format(targetWorkbook, rowIndex, colIndex, True)
                                    targetSheet.write_url(rowIndex, colIndex, cell.hyperlink.target, cell_format, cell.value)
                                else:
                                    # 获取或创建普通格式
                                    cell_format = self._create_format(targetWorkbook, rowIndex, colIndex)
                                    # 写入单元格值
                                    targetSheet.write(rowIndex, colIndex, cell.value, cell_format)
                
                    # 统一设置行高
                    default_height = self._format_config['default_row_height']
                    for row in range(0, sourceSheet.max_row):
                        targetSheet.set_row(row, default_height)
                
                # 保存并关闭工作簿
                targetWorkbook.close()
                sourceWorkbook.close()


    def _copy_file(self, src, dest):
        dest.touch(exist_ok=True)
        dest.write_bytes(src.read_bytes())
    
    def _get_sheet_name(self, index, defaultName):
        if index == 0:
            return "按专业"
        elif index == 1:
            return "按岗位"
        else:
            return defaultName

    def _create_format(self, workbook:xlsxwriter.Workbook, row, col, has_hyperlink=False):
        """
        创建单元格格式对象
        
        Args:
            workbook: xlsxwriter工作簿对象
            row: 行索引
            col: 列索引
            has_hyperlink: 是否包含超链接
            
        Returns:
            单元格格式对象
        """
        # 创建新的格式对象
        cell_format = workbook.add_format()
        
        # 应用格式
        cell_format = self._apply_format(workbook, row, col, cell_format)
        
        # 如果有超链接，设置超链接格式
        if has_hyperlink:
            cell_format.set_hyperlink()
            
        return cell_format