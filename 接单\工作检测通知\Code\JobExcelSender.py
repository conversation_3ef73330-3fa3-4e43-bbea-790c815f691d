from WecomRobot import WecomRobot
from Executor import Executor
from pathlib import Path
from datetime import datetime

import logging
logger = logging.getLogger(__name__)


class JobExcelSender(Executor):
    def __init__(self, input_directory_path: str, webhook_key: str, debug_key: str, is_debug:bool):
        self._input_directory_path = input_directory_path
        self._is_debug = is_debug
        if self._is_debug:
            self._wecom_robot = WecomRobot(webhook_key=debug_key)
        else:
            self._wecom_robot = WecomRobot(webhook_key=webhook_key)
    def execute(self):
        now = datetime.now()
        formattedDate = now.strftime("%Y年%m月%d日")
        self._wecom_robot.send_markdown(f'''**{formattedDate}**推送岗位更新通知<font color=\"warning\">开始</font>，请等待结束通知''')

        inputDirectory = Path(self._input_directory_path)
        # 递归遍历文件夹中的所有文件夹
        for item in inputDirectory.iterdir():
            if item.is_dir():  # 判断是否为文件夹
                self._handle_directory(item)
                if self._is_debug:
                    return None

        self._wecom_robot.send_markdown(f'''**{formattedDate}**推送岗位更新通知<font color=\"info\">结束</font>''')
        self._wecom_robot.send_message(f"请将岗位表转发到投递群", ["@all"])

    def _handle_directory(self, directory:Path) -> int:
        logger.info(f"正在处理文件夹{directory}")
        self._wecom_robot.send_message(f"{directory.name}")

        for sourcePath in directory.iterdir():
            fileID = self._wecom_robot.upload_file(sourcePath)
            self._wecom_robot.send_file(fileID)
