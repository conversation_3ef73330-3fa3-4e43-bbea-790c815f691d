<template>
  <div class="url-manager-container">
    <header class="page-header">
      <h1>URL管理系统</h1>
      <p class="sub-title">管理岗位爬取链接</p>
      <button class="back-btn" @click="goBack">返回岗位列表</button>
    </header>
    
    <section class="content-panel">
      <div class="panel-header">
        <h2>URL列表</h2>
        <button class="add-btn" @click="openAddModal">新增URL</button>
      </div>
      
      <div class="table-wrapper">
        <table class="data-table" v-if="urlList.length > 0">
          <thead>
            <tr>
              <th>ID</th>
              <th>URL</th>
              <th>类型</th>
              <th>状态</th>
              <th>请求参数类型</th>
              <th>详情地址</th>
              <th>创建时间</th>
              <th>更新时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="url in urlList" :key="url.id">
              <td>{{ url.id }}</td>
              <td class="url-cell">{{ url.url }}</td>
              <td>{{ url.type || '-' }}</td>
              <td>{{ url.status || '-' }}</td>
              <td>{{ url.Category }}</td>
              <td class="url-cell">{{ url.detail_url || '-' }}</td>
              <td>{{ url.created_at }}</td>
              <td>{{ url.updated_at }}</td>
              <td>
                <div class="action-buttons">
                  <button class="edit-btn" @click="openEditModal(url)">编辑</button>
                  <button class="delete-btn" @click="confirmDelete(url)">删除</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        
        <div v-else-if="loading" class="loading-placeholder">
          <div class="spinner"></div>
          <p>加载中...</p>
        </div>
        
        <div v-else class="empty-placeholder">
          <p>暂无URL数据</p>
          <button class="add-btn" @click="openAddModal">添加第一个URL</button>
        </div>
      </div>
    </section>
    
    <!-- 编辑/新增URL弹窗 -->
    <div class="url-modal" v-if="showModal" @click.self="closeModal">
      <div class="url-modal-content">
        <button class="close-btn" @click="closeModal">&times;</button>
        <div class="url-modal-header">
          <h3>{{ isEditing ? '编辑URL' : '新增URL' }}</h3>
        </div>
        
        <div class="url-modal-body">
          <div class="form-group">
            <label>URL <span class="required">*</span></label>
            <input type="text" v-model="urlForm.url" placeholder="请输入URL">
            <div class="help-text">例如: https://example.com</div>
          </div>
          
          <div class="form-group">
            <label>类型</label>
            <select v-model="urlForm.type" class="select-input">
              <option value="北森">北森</option>
              <option value="智联">智联</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>状态 <span class="required">*</span></label>
            <select v-model="urlForm.status" class="select-input">
              <option value="启用">启用</option>
              <option value="禁用">禁用</option>
            </select>
          </div>
          
          <div class="form-group" v-if="urlForm.type !== '智联'">
            <label>请求参数类型 <span class="required">*</span></label>
            <input type="number" v-model="urlForm.Category" placeholder="请输入分类编号">
          </div>
          
          <div class="form-group" v-if="urlForm.type !== '智联'">
            <label>详情地址</label>
            <input type="text" v-model="urlForm.detail_url" placeholder="请输入详情地址">
          </div>
          
          <div class="form-actions">
            <button class="cancel-btn" @click="closeModal">取消</button>
            <button class="save-btn" @click="saveUrl" :disabled="isSaving">
              {{ isSaving ? '保存中...' : '保存' }}
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 删除确认弹窗 -->
    <div class="confirm-modal" v-if="showDeleteConfirm" @click.self="cancelDelete">
      <div class="confirm-modal-content">
        <div class="confirm-modal-header">
          <h3>确认删除</h3>
        </div>
        
        <div class="confirm-modal-body">
          <p class="confirm-message">确定要删除以下URL吗？此操作不可撤销。</p>
          <div class="url-preview">{{ deleteUrl ? deleteUrl.url : '' }}</div>
        </div>
        
        <div class="confirm-actions">
          <button class="cancel-btn" @click="cancelDelete">取消</button>
          <button class="delete-btn" @click="deleteUrlItem" :disabled="isDeleting">
            {{ isDeleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { urlApi } from '../utils/request'

export default {
  name: 'UrlManager',
  
  setup() {
    const router = useRouter()
    
    // 状态
    const loading = ref(true)
    const urlList = ref([])
    const showModal = ref(false)
    const isEditing = ref(false)
    const isSaving = ref(false)
    const showDeleteConfirm = ref(false)
    const deleteUrl = ref(null)
    const isDeleting = ref(false)
    
    const urlForm = reactive({
      id: null,
      url: '',
      type: '',
      status: '启用',
      Category: '',
      detail_url: ''
    })
    const fetchUrls = async () => {
      loading.value = true
      try {
        const res = await urlApi.getUrls()
        urlList.value = (res.data || []).map(item => ({
          ...item,
          status: item.status === 1 ? '启用' : '禁用'
        }))
      } catch (error) {
        console.error('获取URL列表失败', error)
        alert('获取URL列表失败：' + (error.message || '未知错误'))
      } finally {
        loading.value = false
      }
    }
    
    const goBack = () => {
      router.push('/')
    }
    
    const openAddModal = () => {
      isEditing.value = false
      Object.keys(urlForm).forEach(key => {
        urlForm[key] = key === 'id' ? null : (key === 'status' ? '启用' : '')
      })
      showModal.value = true
    }
    
    const openEditModal = (url) => {
      isEditing.value = true
      Object.keys(urlForm).forEach(key => {
        if (key === 'status') {
          urlForm.status = url.status === 1 || url.status === '启用' ? '启用' : '禁用'
        } else {
          urlForm[key] = url[key] !== undefined ? url[key] : (key === 'status' ? '启用' : '')
        }
      })
      showModal.value = true
    }
    
    const closeModal = () => {
      if (isSaving.value) return
      showModal.value = false
    }
    
    const saveUrl = async () => {
      if (!urlForm.url.trim()) {
        alert('请输入URL')
        return
      }
      
      if (urlForm.type !== '智联' && !urlForm.Category) {
        alert('请输入分类编号')
        return
      }
      
      if (urlForm.type === '智联') {
        urlForm.Category = ''
        urlForm.detail_url = ''
      }

      const statusValue = urlForm.status === '启用' ? 1 : 0
      const submitData = { ...urlForm, status: statusValue }

      isSaving.value = true
      try {
        let res
        if (isEditing.value) {
          res = await urlApi.updateUrl(urlForm.id, submitData)
        
          const index = urlList.value.findIndex(item => item.id === urlForm.id)
          if (index !== -1) {
            urlList.value[index] = { ...res.data, status: res.data.status === 1 ? '启用' : '禁用' }
          }
          
          alert('URL更新成功')
        } else {
          res = await urlApi.addUrl(submitData)
        
          urlList.value.unshift({ ...res.data, status: res.data.status === 1 ? '启用' : '禁用' })
          
          alert('URL添加成功')
        }
        
        showModal.value = false
      } catch (error) {
        console.error('保存URL失败', error)
        alert('保存失败：' + (error.message || '未知错误'))
      } finally {
        isSaving.value = false
      }
    }
    
    const confirmDelete = (url) => {
      deleteUrl.value = url
      showDeleteConfirm.value = true
    }
    
    const cancelDelete = () => {
      showDeleteConfirm.value = false
      deleteUrl.value = null
    }
    
    const deleteUrlItem = async () => {
      if (!deleteUrl.value || !deleteUrl.value.id) return
      
      isDeleting.value = true
      try {
        await urlApi.deleteUrl(deleteUrl.value.id)
        
        urlList.value = urlList.value.filter(item => item.id !== deleteUrl.value.id)
        
        alert('URL删除成功')
        showDeleteConfirm.value = false
        deleteUrl.value = null
      } catch (error) {
        console.error('删除URL失败', error)
        alert('删除失败：' + (error.message || '未知错误'))
      } finally {
        isDeleting.value = false
      }
    }
    
    onMounted(() => {
      fetchUrls()
    })
    
    return {
      loading,
      urlList,
      showModal,
      isEditing,
      isSaving,
      urlForm,
      showDeleteConfirm,
      deleteUrl,
      isDeleting,
      goBack,
      openAddModal,
      openEditModal,
      closeModal,
      saveUrl,
      confirmDelete,
      cancelDelete,
      deleteUrlItem
    }
  }
}
</script>

<style scoped>
:root {
  --primary-blue: #1a4ad1;
  --secondary-blue: #0099ff;
  --primary-red: #e7173f;
  --secondary-red: #ff4d6a;
  --text-color: #333;
  --light-gray: #f5f6fa;
  --medium-gray: #e0e0e0;
  --dark-gray: #95a5a6;
  --white: #ffffff;
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  --card-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  --radius: 12px;
  --btn-radius: 8px;
}

.url-manager-container {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px 0;
  background: linear-gradient(135deg, var(--primary-red, #e7173f), var(--secondary-blue, #0099ff));
  color: white;
  border-radius: var(--radius, 12px);
  box-shadow: var(--shadow, 0 10px 30px rgba(0, 0, 0, 0.15));
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(255,255,255,0.2), transparent 70%);
  pointer-events: none;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  letter-spacing: 1px;
  text-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.sub-title {
  font-size: 1.1rem;
  opacity: 0.9;
  letter-spacing: 0.5px;
}

.back-btn {
  position: absolute;
  top: 20px;
  left: 20px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: var(--btn-radius);
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  gap: 5px;
}

.back-btn::before {
  content: '←';
  display: inline-block;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateX(-3px);
}

.content-panel {
  background-color: #ffffff;
  padding: 25px;
  border-radius: var(--radius, 12px);
  box-shadow: var(--card-shadow, 0 5px 15px rgba(0, 0, 0, 0.08));
  flex: 1;
  display: flex;
  flex-direction: column;
  border-top: 5px solid var(--secondary-blue, #0099ff);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.panel-header h2 {
  font-size: 1.5rem;
  color: #333;
  margin: 0;
}

.add-btn {
  padding: 10px 20px;
  background: linear-gradient(to right, var(--primary-blue), var(--secondary-blue));
  color: rgb(0, 0, 0);
  border: none;
  border-radius: var(--btn-radius);
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.add-btn::before {
  content: '+';
  display: inline-block;
  font-size: 1.2em;
  font-weight: 600;
}

.add-btn:hover {
  box-shadow: 0 4px 10px rgba(26, 74, 209, 0.2);
  transform: translateY(-2px);
}

.table-wrapper {
  overflow-x: auto;
  flex: 1;
}

.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  white-space: nowrap;
}

.data-table th {
  background: linear-gradient(to right, #f1f5fd, #e8edf8);
  padding: 15px;
  text-align: left;
  font-weight: 600;
  position: relative;
  color: #444;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 2px solid #e0e6f0;
}

.data-table td {
  padding: 15px;
  border-bottom: 1px solid rgba(224, 230, 240, 0.6);
  vertical-align: middle;
  color: #555;
}

.data-table tr:hover td {
  background-color: rgba(26, 74, 209, 0.03);
}

.url-cell {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.edit-btn, .delete-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s;
}

.edit-btn {
  background: #f0f4ff;
  color: var(--primary-blue);
}

.edit-btn:hover {
  background: #e0e8ff;
}

.delete-btn {
  background: #fff0f0;
  color: var(--primary-red);
}

.delete-btn:hover {
  background: #ffe0e0;
}

.loading-placeholder, .empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  text-align: center;
}

.loading-placeholder p, .empty-placeholder p {
  margin: 20px 0;
  color: #888;
  font-size: 16px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary-blue);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* URL操作弹窗 */
.url-modal, .confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1002;
  backdrop-filter: blur(5px);
}

.url-modal-content, .confirm-modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  animation: modal-fade 0.3s ease-out;
}

.url-modal-content {
  width: 90%;
  max-width: 600px;
}

.confirm-modal-content {
  width: 90%;
  max-width: 450px;
}

.url-modal-header, .confirm-modal-header {
  padding: 20px 30px;
  background: linear-gradient(to right, #f0f4ff, #e5ebff);
  border-bottom: 1px solid #eaedf5;
}

.url-modal-header h3, .confirm-modal-header h3 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.url-modal-body {
  padding: 25px 30px;
}

.confirm-modal-body {
  padding: 25px 30px 15px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #444;
  margin-bottom: 8px;
}

.required {
  color: var(--primary-red);
}

.form-group input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 15px;
  transition: all 0.2s;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(26, 74, 209, 0.1);
}

.help-text {
  font-size: 12px;
  color: #888;
  margin-top: 5px;
}

.form-actions, .confirm-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
}

.confirm-actions {
  padding: 15px 30px 25px;
  border-top: 1px solid #eee;
}

.cancel-btn, .save-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background: #f0f2f7;
  color: #666;
}

.cancel-btn:hover {
  background: #e6e9f0;
  color: #444;
}

.save-btn {
  background: linear-gradient(to right, var(--primary-blue), var(--secondary-blue));
  color: rgb(0, 0, 0);
}

.save-btn:hover:not(:disabled) {
  box-shadow: 0 4px 10px rgba(26, 74, 209, 0.2);
  transform: translateY(-1px);
}

.save-btn:disabled, .delete-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.confirm-message {
  font-size: 16px;
  color: #444;
  margin-bottom: 15px;
}

.url-preview {
  padding: 12px 15px;
  background: #f8f9fd;
  border-radius: 6px;
  font-size: 15px;
  color: #333;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-all;
}

.close-btn {
  position: absolute;
  right: 15px;
  top: 15px;
  background: none;
  border: none;
  font-size: 22px;
  color: #666;
  cursor: pointer;
  z-index: 10;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #333;
}

@keyframes modal-fade {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 25px 0;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  .back-btn {
    font-size: 13px;
    padding: 6px 12px;
  }
  
  .data-table {
    font-size: 14px;
  }
  
  .data-table th, .data-table td {
    padding: 12px 10px;
  }
  
  .url-modal-content, .confirm-modal-content {
    width: 95%;
  }
  
  .form-actions, .confirm-actions {
    flex-direction: column-reverse;
  }
  
  .cancel-btn, .save-btn {
    width: 100%;
  }
}

.select-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--medium-gray);
  border-radius: var(--btn-radius);
  font-size: 1rem;
  transition: border-color 0.3s, box-shadow 0.3s;
  background-color: white;
  margin: 5px 0;
}

.select-input:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(26, 74, 209, 0.2);
  outline: none;
}
</style> 