<template>
  <div class="job-list-container">
    <header class="page-header">
      <h1>智能岗位查询系统</h1>
      <p class="sub-title">快速找到适合您的理想职位</p>
      <button class="url-manager-btn" @click="goToUrlManager">URL管理</button>
    </header>
    
    <section class="search-panel">
      <div class="form-row">
        <div class="form-group">
          <label for="company_name">公司名称</label>
          <input type="text" id="company_name" v-model="searchForm.company_name" placeholder="输入公司名称">
        </div>
        <div class="form-group">
          <label for="position_name">岗位名称</label>
          <input type="text" id="position_name" v-model="searchForm.position_name" placeholder="输入岗位名称">
        </div>
        <div class="form-group">
          <label for="work_location">工作地点</label>
          <input type="text" id="work_location" v-model="searchForm.work_location" placeholder="输入工作地点">
        </div>
      </div>
      
      <div class="form-row">
        <div class="form-group">
          <label for="education_req">学历要求</label>
          <select id="education_req" v-model="searchForm.education_req">
            <option value="">不限</option>
            <option value="大专">大专</option>
            <option value="本科">本科</option>
            <option value="硕士">硕士</option>
            <option value="博士">博士</option>
          </select>
        </div>
        <div class="form-group">
          <label for="industry">行业</label>
          <input type="text" id="industry" v-model="searchForm.industry" placeholder="输入行业">
        </div>
        <div class="form-group">
          <label for="major_req">专业要求</label>
          <input type="text" id="major_req" v-model="searchForm.major_req" placeholder="输入专业要求">
        </div>
      </div>
      
      <div class="buttons">
        <button class="btn-primary" @click="handleSearch">搜索岗位</button>
        <button class="btn-secondary" @click="handleReset">重置条件</button>
        <button class="btn-secondary" @click="showDomainManager">批量修改公司名称</button>
      </div>
    </section>
    
    <section class="data-panel">
      <div class="stats">
        <div class="stat-item">
          <div class="stat-value">{{ jobListData.total || 0 }}</div>
          <div class="stat-label">岗位总数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ jobListData.total_pages || 0 }}</div>
          <div class="stat-label">总页数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ pagination.currentPage }}</div>
          <div class="stat-label">当前页</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ pagination.pageSize }}</div>
          <div class="stat-label">每页条数</div>
        </div>
      </div>
      
      <div class="table-wrapper">
        <table class="data-table" v-if="jobListData.jobs && jobListData.jobs.length > 0">
          <thead>
            <tr>
              <th @click="handleSort('id')" class="th-id">
                ID
                <span v-if="sortConfig.field === 'id'" class="sort-icon">
                  {{ sortConfig.order === 'asc' ? '▲' : '▼' }}
                </span>
              </th>
              <th @click="handleSort('position_name')" class="th-major">
                岗位名称
                <span v-if="sortConfig.field === 'position_name'" class="sort-icon">
                  {{ sortConfig.order === 'asc' ? '▲' : '▼' }}
                </span>
              </th>
              <th @click="handleSort('company_name')" class="th-company">
                公司名称
                <span v-if="sortConfig.field === 'company_name'" class="sort-icon">
                  {{ sortConfig.order === 'asc' ? '▲' : '▼' }}
                </span>
              </th>
              <th @click="handleSort('work_location')" class="th-location">
                工作地点
                <span v-if="sortConfig.field === 'work_location'" class="sort-icon">
                  {{ sortConfig.order === 'asc' ? '▲' : '▼' }}
                </span>
              </th>
              <th @click="handleSort('salary')" class="th-salary">
                薪资
                <span v-if="sortConfig.field === 'salary'" class="sort-icon">
                  {{ sortConfig.order === 'asc' ? '▲' : '▼' }}
                </span>
              </th>
              <th @click="handleSort('education_req')" class="th-edu">
                学历要求
                <span v-if="sortConfig.field === 'education_req'" class="sort-icon">
                  {{ sortConfig.order === 'asc' ? '▲' : '▼' }}
                </span>
              </th>
              <th @click="handleSort('major_req')" class="th-major">
                专业要求
                <span v-if="sortConfig.field === 'major_req'" class="sort-icon">
                  {{ sortConfig.order === 'asc' ? '▲' : '▼' }}
                </span>
              </th>
              <th @click="handleSort('position_type')" class="th-type">
                职位类型
                <span v-if="sortConfig.field === 'position_type'" class="sort-icon">
                  {{ sortConfig.order === 'asc' ? '▲' : '▼' }}
                </span>
              </th>
              <th @click="handleSort('work_type')" class="th-type">
                工作类型
                <span v-if="sortConfig.field === 'work_type'" class="sort-icon">
                  {{ sortConfig.order === 'asc' ? '▲' : '▼' }}
                </span>
              </th>
              <th @click="handleSort('post_date')" class="th-date">
                发布时间
                <span v-if="sortConfig.field === 'post_date'" class="sort-icon">
                  {{ sortConfig.order === 'asc' ? '▲' : '▼' }}
                </span>
              </th>
              <th @click="handleSort('end_date')" class="th-date">
                截止时间
                <span v-if="sortConfig.field === 'end_date'" class="sort-icon">
                  {{ sortConfig.order === 'asc' ? '▲' : '▼' }}
                </span>
              </th>
              <th class="th-actions">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="job in jobListData.jobs" :key="job.id">
              <td>{{ job.id }}</td>
              <td class="position-name ellipsis">{{ job.position_name || '-' }}</td>
              <td class="company-name ellipsis">{{ job.company_name || '-' }}</td>
              <td class="ellipsis">{{ job.work_location || '-' }}</td>
              <td class="ellipsis">{{ job.salary || '面议' }}</td>
              <td class="ellipsis">{{ job.education_req || '-' }}</td>
              <td class="ellipsis">{{ job.major_req || '-' }}</td>
              <td class="ellipsis">{{ job.position_type || '-' }}</td>
              <td class="ellipsis">{{ job.work_type || '-' }}</td>
              <td>{{ job.post_date || '-' }}</td>
              <td>{{ formatDate(job.end_date) }}</td>
              <td>
                <div class="action-buttons">
                  <a :href="job.position_link" target="_blank" class="link-btn">详情地址</a>
                  <button class="expand-btn" @click="showDetails(job)">详情</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        
        <div v-else class="no-results">
          未找到符合条件的岗位，请尝试其他搜索条件
        </div>
      </div>
      
      <div class="pagination" v-if="jobListData.total_pages > 1">
        <a 
          class="page-link" 
          :class="{ disabled: pagination.currentPage === 1 }"
          @click="handlePageChange(pagination.currentPage - 1)"
        >上一页</a>
        
        <a 
          v-for="page in paginationPages" 
          :key="page"
          class="page-link" 
          :class="{ active: pagination.currentPage === page }"
          @click="handlePageChange(page)"
        >{{ page }}</a>
        
        <a 
          class="page-link" 
          :class="{ disabled: pagination.currentPage === jobListData.total_pages }"
          @click="handlePageChange(pagination.currentPage + 1)"
        >下一页</a>
      </div>
    </section>
    
    <!-- 详情弹出面板 -->
    <div class="details-modal" v-if="selectedJob" @click.self="closeDetails">
      <div class="details-modal-content">
        <button class="close-btn" @click="closeDetails">&times;</button>
        <div class="details-modal-header">
          <h3 class="job-title">{{ selectedJob.position_name }}</h3>
          <div class="job-meta">
            <div class="company-name">{{ selectedJob.company_name }}</div>
            <div class="salary">{{ selectedJob.salary || '面议' }}</div>
          </div>
          <div class="job-tags">
            <div class="tag">{{ selectedJob.work_location || '地点未知' }}</div>
            <div class="tag">{{ selectedJob.education_req || '不限学历' }}</div>
            <div class="tag">{{ selectedJob.work_type || '全职' }}</div>
          </div>
        </div>

        <div class="details-modal-body">
          <div class="details-section">
            <h4>职位描述</h4>
            <div class="markdown-content" v-html="renderMarkdown(selectedJob.position_desc || '暂无详细描述')"></div>
          </div>
          
          <div class="details-section">
            <h4>岗位要求</h4>
            <div class="markdown-content" v-html="renderMarkdown(selectedJob.position_requirement || '暂无详细要求')"></div>
          </div>
          
          <div class="details-grid">
            <div class="details-item">
              <div class="details-label">公司类型</div>
              <div class="details-value">{{ selectedJob.company_type || '-' }}</div>
            </div>
            <div class="details-item">
              <div class="details-label">所属行业</div>
              <div class="details-value">{{ selectedJob.industry || '-' }}</div>
            </div>
            <div class="details-item">
              <div class="details-label">职位类型</div>
              <div class="details-value">{{ selectedJob.job_type || '-' }}</div>
            </div>
            <div class="details-item">
              <div class="details-label">专业要求</div>
              <div class="details-value">{{ selectedJob.major_req || '-' }}</div>
            </div>
            <div class="details-item">
              <div class="details-label">发布时间</div>
              <div class="details-value">{{ selectedJob.post_date || '-' }}</div>
            </div>
            <div class="details-item">
              <div class="details-label">招聘状态</div>
              <div class="details-value">{{ formatDate(selectedJob.end_date) }}</div>
            </div>
          </div>

          <div class="details-actions">
            <a :href="selectedJob.position_link" target="_blank" class="apply-btn">岗位详情</a>
            <a v-if="selectedJob.backup_link" 
               :href="selectedJob.backup_link" 
               target="_blank" 
               class="backup-link">备用链接</a>
            <button class="edit-btn" @click="openEditJob(selectedJob)">编辑岗位</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 编辑岗位弹窗 -->
    <div class="edit-modal" v-if="showEditModal" @click.self="closeEditModal">
      <div class="edit-modal-content">
        <button class="close-btn" @click="closeEditModal">&times;</button>
        <div class="edit-modal-header">
          <h3>编辑岗位信息</h3>
        </div>
        
        <div class="edit-modal-body">
          <div class="form-grid">
            <div class="form-group">
              <label>公司名称</label>
              <input type="text" v-model="editForm.company_name" placeholder="公司名称">
            </div>
            <div class="form-group">
              <label>公司类型</label>
              <input type="text" v-model="editForm.company_type" placeholder="公司类型">
            </div>
            <div class="form-group">
              <label>行业</label>
              <input type="text" v-model="editForm.industry" placeholder="行业">
            </div>
            <div class="form-group">
              <label>岗位名称</label>
              <input type="text" v-model="editForm.position_name" placeholder="岗位名称">
            </div>
            <div class="form-group">
              <label>工作地点</label>
              <input type="text" v-model="editForm.work_location" placeholder="工作地点">
            </div>
            <div class="form-group">
              <label>学历要求</label>
              <select v-model="editForm.education_req">
                <option value="">不限</option>
                <option value="大专">大专</option>
                <option value="本科">本科</option>
                <option value="硕士">硕士</option>
                <option value="博士">博士</option>
              </select>
            </div>
            <div class="form-group">
              <label>专业要求</label>
              <input type="text" v-model="editForm.major_req" placeholder="专业要求">
            </div>
            <div class="form-group">
              <label>薪资</label>
              <input type="text" v-model="editForm.salary" placeholder="薪资">
            </div>
            <div class="form-group">
              <label>招聘类型</label>
              <input type="text" v-model="editForm.job_type" placeholder="招聘类型">
            </div>
            <div class="form-group">
              <label>岗位类型</label>
              <input type="text" v-model="editForm.position_type" placeholder="岗位类型">
            </div>
            <div class="form-group">
              <label>工作类型</label>
              <input type="text" v-model="editForm.work_type" placeholder="工作类型">
            </div>
            <div class="form-group">
              <label>发布时间</label>
              <input type="text" v-model="editForm.post_date" placeholder="发布时间 (YYYY-MM-DD)">
            </div>
            <div class="form-group">
              <label>截止时间</label>
              <input type="text" v-model="editForm.end_date" placeholder="截止时间 (YYYY-MM-DD) 或留空表示长期">
            </div>
            <div class="form-group full-width">
              <label>岗位链接</label>
              <input type="text" v-model="editForm.position_link" placeholder="岗位链接">
            </div>
            <div class="form-group full-width">
              <label>备用链接</label>
              <input type="text" v-model="editForm.backup_link" placeholder="备用链接">
            </div>
          </div>
          
          <div class="form-group full-width">
            <label>岗位职责</label>
            <textarea v-model="editForm.position_desc" placeholder="岗位职责" rows="6"></textarea>
            <div class="help-text">支持Markdown格式</div>
          </div>
          
          <div class="form-group full-width">
            <label>岗位要求</label>
            <textarea v-model="editForm.position_requirement" placeholder="岗位要求" rows="6"></textarea>
            <div class="help-text">支持Markdown格式</div>
          </div>
          
          <div class="form-actions">
            <button class="cancel-btn" @click="closeEditModal">取消</button>
            <button class="save-btn" @click="saveJob" :disabled="isSaving">
              {{ isSaving ? '保存中...' : '保存' }}
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 域名管理弹窗 -->
    <div class="domain-modal" v-if="showDomainModal" @click.self="closeDomainModal">
      <div class="domain-modal-content">
        <button class="close-btn" @click="closeDomainModal">&times;</button>
        <div class="domain-modal-header">
          <h3>批量修改公司名称</h3>
        </div>
        
        <div class="domain-modal-body">
          <div v-if="domainLoading" class="domain-loading">
            <div class="spinner"></div>
            <p>加载中...</p>
          </div>
          
          <div v-else-if="domainList.length === 0" class="no-domains">
            暂无可用域名数据
          </div>
          
          <div v-else class="domain-list">
            <div class="domain-item" v-for="(domain, index) in domainList" :key="index">
              <div class="domain-name">{{ domain }}</div>
              <button class="edit-name-btn" @click="openNameEditor(domain)">修改</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 修改名称弹窗 -->
    <div class="name-editor-modal" v-if="showNameEditor" @click.self="closeNameEditor">
      <div class="name-editor-content">
        <button class="close-btn" @click="closeNameEditor">&times;</button>
        <div class="name-editor-header">
          <h3>修改公司名称</h3>
        </div>
        
        <div class="name-editor-body">
          <div class="name-editor-info">
            <div class="info-item">
              <span class="info-label">当前域名:</span>
              <span class="info-value">{{ currentDomain }}</span>
            </div>
          </div>
          
          <div class="name-editor-form">
            <div class="form-group">
              <label>新公司名称</label>
              <input type="text" v-model="newCompanyName" placeholder="请输入新的公司名称">
            </div>
            
            <div class="form-actions">
              <button class="cancel-btn" @click="closeNameEditor">取消</button>
              <button class="save-btn" @click="updateCompanyName" :disabled="isUpdating">
                {{ isUpdating ? '更新中...' : '更新' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="loading" v-if="loading">
      <div class="spinner"></div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { jobApi } from '../utils/request'
import { marked } from 'marked'

export default {
  name: 'JobList',
  
  setup() {
    const router = useRouter()
    // 加载状态
    const loading = ref(false)
    
    // 展开行的ID集合
    const expandedRows = ref([])
    
    // 搜索表单
    const searchForm = reactive({
      company_name: '',
      position_name: '',
      work_location: '',
      education_req: '',
      industry: '',
      major_req: ''
    })
    
    // 排序配置
    const sortConfig = reactive({
      field: '',
      order: 'asc'
    })
    
    // 分页配置
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10
    })
    
    // 岗位数据
    const jobListData = reactive({
      jobs: [],
      total: 0,
      total_pages: 0,
      page: 1,
      size: 10
    })
    
    // 计算分页按钮
    const paginationPages = computed(() => {
      const totalPages = jobListData.total_pages || 0
      const currentPage = pagination.currentPage
      
      if (totalPages <= 5) {
        return Array.from({ length: totalPages }, (_, i) => i + 1)
      }
      
      const startPage = Math.max(1, currentPage - 2)
      const endPage = Math.min(totalPages, startPage + 4)
      
      return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i)
    })
    
    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr || dateStr === '0001-01-01 00:00:00') {
        return '长期招聘'
      }
      return dateStr
    }
    
    // 切换展开/收起行
    const toggleExpandRow = (jobId) => {
      const index = expandedRows.value.indexOf(jobId)
      if (index > -1) {
        expandedRows.value.splice(index, 1)
      } else {
        expandedRows.value.push(jobId)
      }
    }
    
    // 选中的岗位数据（用于详情展示）
    const selectedJob = ref(null)
    
    // 显示详情
    const showDetails = (job) => {
      selectedJob.value = job
    }
    
    // 关闭详情
    const closeDetails = () => {
      selectedJob.value = null
    }
    
    // 获取岗位数据
    const fetchJobs = async () => {
      loading.value = true
      
      try {
        // 构建查询参数
        const params = {
          page: pagination.currentPage,
          size: pagination.pageSize
        }
        
        // 添加排序参数
        if (sortConfig.field) {
          params.sort_field = sortConfig.field
          params.sort_order = sortConfig.order
        }
        
        // 添加搜索条件
        Object.keys(searchForm).forEach(key => {
          if (searchForm[key] && searchForm[key].trim() !== '') {
            params[key] = searchForm[key]
          }
        })
        
        // 获取数据
        const res = await jobApi.getJobs(params)
        
        // 更新数据
        Object.assign(jobListData, res.data)
        pagination.currentPage = res.data.page || 1
        
        // 重置展开行
        expandedRows.value = []
        
      } catch (error) {
        console.error('获取岗位数据失败', error)
      } finally {
        loading.value = false
      }
    }
    
    // 处理搜索
    const handleSearch = () => {
      pagination.currentPage = 1
      fetchJobs()
    }
    
    // 处理重置
    const handleReset = () => {
      // 重置搜索表单
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      
      // 重置排序
      sortConfig.field = ''
      sortConfig.order = 'asc'
      
      // 重置分页
      pagination.currentPage = 1
      
      // 重新获取数据
      fetchJobs()
    }
    
    // 处理排序
    const handleSort = (field) => {
      if (sortConfig.field === field) {
        sortConfig.order = sortConfig.order === 'asc' ? 'desc' : 'asc'
      } else {        sortConfig.field = field
        sortConfig.order = 'asc'
      }

      fetchJobs()
    }
    
    const handlePageChange = (page) => {
      if (page < 1 || page > jobListData.total_pages || page === pagination.currentPage) {
        return
      }
      
      pagination.currentPage = page
      fetchJobs()
      
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
    const renderMarkdown = (content) => {
      return marked(content)
    }
    
    const showEditModal = ref(false)
    const isSaving = ref(false)
    const editForm = reactive({
      company_name: '',
      company_type: '',
      industry: '',
      position_name: '',
      work_location: '',
      education_req: '',
      major_req: '',
      salary: '',
      job_type: '',
      position_type: '',
      work_type: '',
      post_date: '',
      end_date: '',
      position_link: '',
      backup_link: '',
      position_desc: '',
      position_requirement: ''
    })
    
    const openEditJob = (job) => {
      Object.keys(editForm).forEach(key => {
        editForm[key] = job[key] || ''
      })
      showEditModal.value = true
    }
    
    const closeEditModal = () => {
      if (isSaving.value) return
      showEditModal.value = false
    }
    
    const saveJob = async () => {
      if (!selectedJob.value || !selectedJob.value.id) return
      
      try {
        isSaving.value = true
        
        const res = await jobApi.updateJob(selectedJob.value.id, editForm)
        
        selectedJob.value = res.data
        
        const index = jobListData.jobs.findIndex(job => job.id === res.data.id)
        if (index !== -1) {
          jobListData.jobs[index] = res.data
        }
        
        showEditModal.value = false
        
        alert('岗位信息更新成功')
      } catch (error) {
        console.error('更新岗位信息失败', error)
        alert(`更新失败: ${error.message || '未知错误'}`)
      } finally {
        isSaving.value = false
      }
    }
    
    const showDomainModal = ref(false)
    const domainList = ref([])
    const domainLoading = ref(false)
    
    const showNameEditor = ref(false)
    const currentDomain = ref('')
    const newCompanyName = ref('')
    const isUpdating = ref(false)

    const showDomainManager = async () => {
      showDomainModal.value = true
      await fetchDomains()
    }
    
    // 关闭域名管理弹窗
    const closeDomainModal = () => {
      showDomainModal.value = false
    }
    
    // 获取域名列表
    const fetchDomains = async () => {
      domainLoading.value = true
      try {
        const res = await jobApi.getBackupLinks()
        domainList.value = res.data.domains || []
      } catch (error) {
        console.error('获取域名列表失败', error)
        alert('获取域名列表失败：' + (error.message || '未知错误'))
      } finally {
        domainLoading.value = false
      }
    }
    
    // 打开名称编辑器
    const openNameEditor = (domain) => {
      currentDomain.value = domain
      newCompanyName.value = ''
      showNameEditor.value = true
    }
    
    // 关闭名称编辑器
    const closeNameEditor = () => {
      if (isUpdating.value) return
      showNameEditor.value = false
    }
    
    // 更新公司名称
    const updateCompanyName = async () => {
      if (!currentDomain.value || !newCompanyName.value.trim()) {
        alert('请输入有效的公司名称')
        return
      }
      
      isUpdating.value = true
      try {
        await jobApi.updateJobNameByUrl({
          url: currentDomain.value,
          name: newCompanyName.value.trim()
        })
        
        alert('公司名称更新成功')
        showNameEditor.value = false
        
        // 如果当前页面有相关数据，刷新列表
        fetchJobs()
      } catch (error) {
        console.error('更新公司名称失败', error)
        alert('更新失败：' + (error.message || '未知错误'))
      } finally {
        isUpdating.value = false
      }
    }
    
    // 跳转到URL管理页面
    const goToUrlManager = () => {
      router.push('/url-manager')
    }
    
    // 组件挂载时获取数据
    onMounted(() => {
      fetchJobs()
    })
    
    return {
      loading,
      searchForm,
      sortConfig,
      pagination,
      jobListData,
      paginationPages,
      selectedJob,
      showEditModal,
      isSaving,
      editForm,
      formatDate,
      renderMarkdown,
      showDetails,
      closeDetails,
      openEditJob,
      closeEditModal,
      saveJob,
      handleSearch,
      handleReset,
      handleSort,
      handlePageChange,
      // 域名管理相关
      showDomainModal,
      domainList,
      domainLoading,
      showNameEditor,
      currentDomain,
      newCompanyName,
      isUpdating,
      showDomainManager,
      closeDomainModal,
      openNameEditor,
      closeNameEditor,
      updateCompanyName,
      // URL管理
      goToUrlManager
    }
  }
}
</script>

<style scoped>
:root {
  --primary-blue: #1a4ad1;
  --secondary-blue: #0099ff;
  --primary-red: #e7173f;
  --secondary-red: #ff4d6a;
  --text-color: #333;
  --light-gray: #f5f6fa;
  --medium-gray: #e0e0e0;
  --dark-gray: #95a5a6;
  --white: #ffffff;
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  --card-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  --radius: 12px;
  --btn-radius: 8px;
}

.job-list-container {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px 0;
  background: linear-gradient(135deg, var(--primary-red, #e7173f), var(--secondary-blue, #0099ff));
  color: white;
  border-radius: var(--radius, 12px);
  box-shadow: var(--shadow, 0 10px 30px rgba(0, 0, 0, 0.15));
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(255,255,255,0.2), transparent 70%);
  pointer-events: none;
}

h1 {
  font-size: 2.8rem;
  margin-bottom: 15px;
  letter-spacing: 1px;
  text-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.sub-title {
  font-size: 1.2rem;
  opacity: 0.9;
  letter-spacing: 0.5px;
}

.search-panel {
  background-color: #ffffff;
  padding: 25px;
  border-radius: var(--radius, 12px);
  box-shadow: var(--card-shadow, 0 5px 15px rgba(0, 0, 0, 0.08));
  margin-bottom: 30px;
  border-top: 5px solid var(--primary-blue, #1a4ad1);
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  flex: 1;
  min-width: 200px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #444;
  font-size: 15px;
}

input, select {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: var(--btn-radius, 8px);
  font-size: 15px;
  transition: all 0.3s;
  background-color: #fcfcfd;
}

input:focus, select:focus {
  outline: none;
  border-color: var(--secondary-blue, #0099ff);
  box-shadow: 0 0 0 3px rgba(0, 153, 255, 0.2);
  background-color: #ffffff;
}

.buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 25px;
}

.btn-primary, .btn-secondary {
  padding: 12px 30px;
  border: none;
  border-radius: var(--btn-radius, 8px);
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s;
  letter-spacing: 0.5px;
}

.btn-primary {
  background: linear-gradient(to right, var(--primary-red, #e7173f), var(--secondary-red, #ff4d6a));
  color: white;
  box-shadow: 0 4px 10px rgba(231, 23, 63, 0.3);
}

.btn-primary:hover {
  box-shadow: 0 6px 15px rgba(231, 23, 63, 0.4);
  transform: translateY(-2px);
}

.btn-secondary {
  background: linear-gradient(to right, #f0f2f7, #e6e9f0);
  color: #666;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
  color: #444;
}

.data-panel {
  background-color: #ffffff;
  padding: 25px;
  border-radius: var(--radius, 12px);
  box-shadow: var(--card-shadow, 0 5px 15px rgba(0, 0, 0, 0.08));
  flex: 1;
  display: flex;
  flex-direction: column;
  border-top: 5px solid var(--secondary-blue, #0099ff);
}

.stats {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
  gap: 15px;
}

.stat-item {
  text-align: center;
  flex: 1;
  min-width: 120px;
  padding: 15px;
  background: linear-gradient(135deg, #f6f7fc, #eef1f9);
  border-radius: var(--btn-radius, 8px);
}

.stat-value {
  font-size: 1.8rem;
  font-weight: bold;
  background: linear-gradient(to right, var(--primary-red, #e7173f), var(--primary-blue, #1a4ad1));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.stat-label {
  font-size: 0.95rem;
  color: #95a5a6;
  margin-top: 5px;
  font-weight: 500;
}

.table-wrapper {
  overflow-x: auto;
  overflow-y: visible;
  margin: 0 -25px;
  padding: 0 25px;
}

.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  white-space: nowrap;
}

.data-table thead {
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table th {
  background: linear-gradient(to right, #f1f5fd, #e8edf8);
  padding: 15px;
  text-align: left;
  font-weight: 600;
  position: relative;
  color: #444;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 2px solid #e0e6f0;
  cursor: pointer;
  transition: all 0.2s;
}

.data-table th:hover {
  background: linear-gradient(to right, #e8edf8, #dfe6f5);
}

.th-id { width: 60px; }
.th-company { width: 120px; }
.th-location { width: 120px; }
.th-salary { width: 80px; }
.th-edu { width: 100px; }
.th-major { width: 120px; }
.th-type { width: 100px; }
.th-date { width: 120px; }
.th-actions { width: 100px; text-align: center; }

.sort-icon {
  position: absolute;
  right: 10px;
  color: var(--primary-red, #e7173f);
  font-size: 10px;
}

.data-table td {
  padding: 15px;
  border-bottom: 1px solid rgba(224, 230, 240, 0.6);
  vertical-align: middle;
  color: #555;
  transition: all 0.2s;
}

.data-table tr:hover td {
  background-color: rgba(26, 74, 209, 0.03);
}

.data-table tr.expanded td {
  background-color: rgba(26, 74, 209, 0.05);
  border-bottom-color: transparent;
}

.company-name {
  font-weight: 600;
  color: var(--primary-blue, #1a4ad1);
}

.position-name {
  color: var(--primary-red, #e7173f);
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.link-btn, .expand-btn {
  display: inline-block;
  padding: 6px 10px;
  font-size: 13px;
  border-radius: 4px;
  text-decoration: none;
  transition: all 0.2s;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  border: none;
}

.link-btn {
  background: linear-gradient(to right, var(--primary-blue, #1a4ad1), var(--secondary-blue, #0099ff));
  color: white;
}

.link-btn:hover {
  box-shadow: 0 2px 5px rgba(26, 74, 209, 0.3);
  transform: translateY(-1px);
}

.expand-btn {
  background: linear-gradient(to right, #f0f2f7, #e6e9f0);
  color: #666;
}

.expand-btn:hover {
  background: linear-gradient(to right, #e6e9f0, #dce0e8);
  color: #444;
}

.details-row {
  background-color: #f8f9fd;
}

.details-cell {
  padding: 0 !important;
}

.details-container {
  padding: 20px;
  background-color: rgba(248, 249, 253, 0.8);
  border-bottom: 1px solid rgba(224, 230, 240, 0.6);
}

.details-section {
  margin-bottom: 20px;
}

.details-section h4 {
  font-size: 16px;
  color: #444;
  margin-bottom: 10px;
  position: relative;
  padding-left: 12px;
}

.details-section h4::before {
  content: '';
  position: absolute;
  left: 0;
  top: 3px;
  bottom: 3px;
  width: 4px;
  background: linear-gradient(to bottom, var(--primary-red, #e7173f), var(--secondary-red, #ff4d6a));
  border-radius: 2px;
}

.details-section pre {
  white-space: pre-wrap;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.6;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  color: #555;
  border: 1px solid #eee;
  margin: 0;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.details-item {
  background-color: white;
  padding: 10px 15px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.details-label {
  font-size: 13px;
  color: #888;
  display: block;
  margin-bottom: 5px;
}

.details-value {
  font-size: 14px;
  color: #444;
  font-weight: 500;
}

.details-link {
  color: var(--primary-blue, #1a4ad1);
  text-decoration: none;
  font-weight: 500;
}

.details-link:hover {
  text-decoration: underline;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  gap: 8px;
  flex-wrap: wrap;
}

.page-link {
  display: block;
  padding: 10px 15px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: var(--btn-radius, 8px);
  color: #333;
  text-decoration: none;
  transition: all 0.2s;
  min-width: 40px;
  text-align: center;
  cursor: pointer;
}

.page-link:hover {
  background-color: #f5f6fa;
  border-color: #d0d0d0;
}

.page-link.active {
  background: linear-gradient(to right, var(--primary-blue, #1a4ad1), var(--secondary-blue, #0099ff));
  color: white;
  border-color: var(--primary-blue, #1a4ad1);
  box-shadow: 0 2px 6px rgba(26, 74, 209, 0.3);
}

.page-link.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.no-results {
  text-align: center;
  padding: 50px 0;
  color: #888;
  font-size: 1.2rem;
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.spinner {
  width: 60px;
  height: 60px;
  border: 5px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 5px solid var(--primary-red, #e7173f);
  border-bottom: 5px solid var(--primary-blue, #1a4ad1);
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .form-group {
    min-width: 100%;
  }
  
  .buttons {
    flex-direction: column;
  }
  
  .btn-primary, .btn-secondary {
    width: 100%;
  }
  
  h1 {
    font-size: 2.2rem;
  }
  
  .stats {
    flex-wrap: wrap;
  }
  
  .stat-item {
    min-width: calc(50% - 10px);
    flex: 0 0 calc(50% - 10px);
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 1.8rem;
  }
  
  .sub-title {
    font-size: 1rem;
  }
  
  .search-panel, .data-panel {
    padding: 15px;
  }
  
  .stat-item {
    min-width: 100%;
    flex: 0 0 100%;
  }
}

/* 详情弹出面板样式 */
.details-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.details-modal-content {
  background: white;
  width: 90%;
  max-width: 850px;
  max-height: 85vh;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modal-fade 0.3s ease-out;
}

@keyframes modal-fade {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.close-btn {
  position: absolute;
  right: 20px;
  top: 20px;
  background: white;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  z-index: 10;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.2s;
}

.close-btn:hover {
  transform: scale(1.1);
  color: #333;
}

.details-modal-header {
  padding: 30px 30px 20px;
  background: linear-gradient(to right, #f0f4ff, #e5ebff);
  border-bottom: 1px solid #eaedf5;
}

.job-title {
  font-size: 24px;
  color: #333;
  margin: 0 0 15px 0;
  padding-right: 30px;
  line-height: 1.3;
}

.job-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.company-name {
  font-size: 16px;
  color: var(--primary-blue);
  font-weight: 500;
}

.salary {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-red);
  background: rgba(231, 23, 63, 0.1);
  padding: 6px 14px;
  border-radius: 20px;
}

.job-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag {
  padding: 6px 12px;
  background: white;
  border-radius: 6px;
  font-size: 14px;
  color: #555;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.details-modal-body {
  padding: 30px;
  overflow-y: auto;
  flex: 1;
}

.details-section {
  margin-bottom: 30px;
}

.details-section h4 {
  font-size: 18px;
  color: #333;
  margin: 0 0 15px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #eaedf5;
  position: relative;
}

.details-section h4::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(to right, var(--primary-red), var(--primary-blue));
}

.markdown-content {
  background: #f9faff;
  padding: 20px;
  border-radius: 8px;
  line-height: 1.6;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin: 1em 0 0.5em;
  color: #333;
}

.markdown-content :deep(p) {
  margin: 0 0 1em;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  padding-left: 1.5em;
  margin-bottom: 1em;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin: 30px 0;
  background: #f5f8ff;
  padding: 20px;
  border-radius: 8px;
}

.details-item {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.details-label {
  font-size: 13px;
  color: #888;
  margin-bottom: 5px;
}

.details-value {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.details-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.apply-btn {
  flex: 1;
  display: block;
  padding: 14px 24px;
  background: linear-gradient(to right, var(--primary-red), var(--secondary-red));
  color: rgb(0, 0, 0);
  text-align: center;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.2s;
  box-shadow: 0 4px 10px rgba(231, 23, 63, 0.25);
}

.apply-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(231, 23, 63, 0.3);
}

.backup-link {
  padding: 14px 20px;
  background: #f0f4ff;
  color: var(--primary-blue);
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s;
}

.backup-link:hover {
  background: #e0e8ff;
}

@media (max-width: 768px) {
  .details-modal-content {
    width: 95%;
    max-height: 90vh;
  }
  
  .job-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .details-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .details-modal-header,
  .details-modal-body {
    padding: 20px;
  }
  
  .job-title {
    font-size: 20px;
  }
}

/* 表格单元格省略号 */
.ellipsis {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 编辑弹窗样式 */
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
  backdrop-filter: blur(5px);
}

.edit-modal-content {
  background: white;
  width: 90%;
  max-width: 900px;
  max-height: 85vh;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modal-fade 0.3s ease-out;
}

.edit-modal-header {
  padding: 20px 30px;
  background: linear-gradient(to right, #f0f4ff, #e5ebff);
  border-bottom: 1px solid #eaedf5;
}

.edit-modal-header h3 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.edit-modal-body {
  padding: 30px;
  overflow-y: auto;
  flex: 1;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #444;
}

.form-group input, 
.form-group select, 
.form-group textarea {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 15px;
  transition: all 0.2s;
}

.form-group input:focus, 
.form-group select:focus, 
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(26, 74, 209, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.help-text {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
}

.cancel-btn, .save-btn, .edit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background: #f0f2f7;
  color: #666;
}

.cancel-btn:hover {
  background: #e6e9f0;
  color: #444;
}

.save-btn {
  background: linear-gradient(to right, var(--primary-blue), var(--secondary-blue));
  color: rgb(0, 0, 0);
}

.save-btn:hover:not(:disabled) {
  box-shadow: 0 4px 10px rgba(26, 74, 209, 0.2);
  transform: translateY(-1px);
}

.save-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.edit-btn {
  background: #f0f4ff;
  color: var(--primary-blue);
}

.edit-btn:hover {
  background: #e0e8ff;
}

@media (max-width: 768px) {
  .edit-modal-content {
    width: 95%;
    max-height: 90vh;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .cancel-btn, .save-btn {
    width: 100%;
  }
}

/* 域名管理弹窗样式 */
.domain-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1002;
  backdrop-filter: blur(5px);
}

.domain-modal-content {
  background: white;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modal-fade 0.3s ease-out;
}

.domain-modal-header {
  padding: 20px 30px;
  background: linear-gradient(to right, #f0f4ff, #e5ebff);
  border-bottom: 1px solid #eaedf5;
}

.domain-modal-header h3 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.domain-modal-body {
  padding: 30px;
  overflow-y: auto;
  flex: 1;
}

.domain-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.domain-loading p {
  margin-top: 15px;
  color: #666;
}

.no-domains {
  text-align: center;
  padding: 40px 0;
  color: #888;
  font-size: 16px;
}

.domain-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.domain-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fd;
  border-radius: 8px;
  border-left: 4px solid var(--primary-blue);
}

.domain-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.edit-name-btn {
  padding: 8px 15px;
  background: #f0f4ff;
  color: var(--primary-blue);
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.edit-name-btn:hover {
  background: #e0e8ff;
}

/* 名称编辑弹窗样式 */
.name-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1003;
  backdrop-filter: blur(5px);
}

.name-editor-content {
  background: white;
  width: 90%;
  max-width: 450px;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  animation: modal-fade 0.3s ease-out;
}

.name-editor-header {
  padding: 20px 30px;
  background: linear-gradient(to right, #f0f4ff, #e5ebff);
  border-bottom: 1px solid #eaedf5;
}

.name-editor-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.name-editor-body {
  padding: 25px;
}

.name-editor-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fd;
  border-radius: 8px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-label {
  font-size: 14px;
  color: #666;
}

.info-value {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  word-break: break-all;
}

@keyframes modal-fade {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.url-manager-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: var(--btn-radius);
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
  backdrop-filter: blur(5px);
}

.url-manager-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .url-manager-btn {
    top: 10px;
    right: 10px;
    padding: 6px 12px;
    font-size: 13px;
  }
}
</style> 