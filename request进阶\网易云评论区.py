import requests
import json
from Crypto.Cipher import AES
import base64

url = "https://music.163.com/weapi/comment/resource/comments/get?csrf_token=2a372cddc6634edadc0bf748cff614a8"
headers = {
    "cookie": "NMTID=00OVYxMH8vwn59ozktKsUQAt5G7EvoAAAGRIWVCQA; _iuqxldmzr_=32; WM_TID=cSR1yZz9dJhEAVVAVAeV5YA7hh%2F5pnym; WEVNSM=1.0.0; WNMCID=oubjoa.1722842171641.01.0; timing_user_id=time_gEFwpsj1KY; JSESSIONID-WYYY=bg91QRuT7iSza7rZWi058c17I7QUNhya%5CIG%2B%5C6Bs4vYem6K6R5HN7wyhR6nEfCV2%5CUww%5C9nEs2d%2Fn%2BWe7wT8qNMg0vnOvyg1sXNOvEV5snKT9Iv%2FB9jj1lfTeMOx%2FMXhMvyKGbAyODPB%5CPp7bllVcceV%2B%2B%2BOeGDjQUuccffA%2Fvrfj4p9%3A1728194097641; WM_NI=M8UeqQd%2BRvGnnko3G7Cqf0g3jRgprCogSKOus9vkW4S0VB1ncNlG5jb17Wp1Y6W2T6mjgaGr%2Fowx3qS6oh6EIHv2o0lns68ZBMTmQKq8cd2Y54CflQGdHRfkkOiZpzwEYTQ%3D; WM_NIKE=9ca17ae2e6ffcda170e2e6ee91ea3c8f9eabb5cb7cacef8ab2d15f938e9a83d66098a99aaadc6ea3e982a7dc2af0fea7c3b92a8fe89b86f45ba8f59e97c666aeb5a1d4c63d81afaad1e639aa8ea991f149adb99dacce7fb1e7ba8cd64091f09aa3b466a192a4a8b862a2eff9b8f33b9bea99d3d73eafb6a982c67aedb1b999b27b91eaa189b54f8b98bad3ee3cb0b2bdd7bb5282939c8ed441b3e8ad85b153f7ec8d87ef5b85bc88b3e561a991a7bbf372b59b97b5d437e2a3; ntes_utid=tid._.XQca0jOqTz1AU0AERELQsdV7hh%252FtpgbA._.0; sDeviceId=YD-djtUGrQcjSBBQlUQQVbR08Xe7fHj%2FQeU; __snaker__id=5576azC4gb8u4LIK; gdxidpyhxdE=5SRXNziBdeYdyokiOpC6RclviHkzrf3j5qLmRQ%2BLnxxJy%2FXGM%5CmNaA%2FOtu3v%2Bz5TPvx0yS2Km0V6A0K3kCtwUh0ciyej7%5ChoPgBMLj%2FuSqtaoHISP5wcHKf%2FncXKpMgfbJxS4D7fHM0CHzuid3NyKb3z0HwBV7uoOaRi5%5Cf0VGzgNu2g%3A1728193206081; MUSIC_U=00C8BC688305D0C8867DFECB9E29DF6DD88BC795CBF5885AB9E85979D25E282EA27E93A71551801B2E78451D6B9D180D5CC905EF5AAA2C71DD156552A2C764A2F6E9B5D192A5A5024C0180BED38AE69BFD3D6A5BDA0965F875F03CC1C3E7274C83E0FEDE2712D24D1FB34DD2AD3ACCA26E8AAC232787384124AF7D180A3582F7DCCF20AE13688373395AEE1D9EAD13F199FBA3381059D44BAA22D3957E1DA46BFABF9A9B2D99594AF4833FC251C932250A96212774917F47CEC389900AA458A8042576D96DAC35BA2533E06FBD2F4525AE3F4C921333EF0458EFCFFC4B60684A5DEBCCD89218EFBD116435B4BA87456D2357456D45CBD56DE8919A006796A450521EA3850EF5449CAE2A9274BDD9F34DDB1EBD73CAB457B4E314E69A7A5E874AE65CFF2A64F4FCD4257FC45869B451E6E27AF36632928D24394736115DA417CC889C369F41BAE8F373636FFE195166A7045E7D5AA377C926F0CCC40E8F6CC07960A6A955F2CA1B08AB9F032A0EB70517F17332786A24D54D2B7EC4E38128218D23; __csrf=2a372cddc6634edadc0bf748cff614a8; __remember_me=true",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36"
}
i7b = {
    "csrf_token": "2a372cddc6634edadc0bf748cff614a8",
    "cursor": "-1",
    "offset": "0",
    "orderType": "1",
    "pageNo": "2",
    "pageSize": "20",
    "rid": "R_SO_4_447925066",
    "threadId": "R_SO_4_447925066"
}


# 加密方法
def encipher(data, key):
    c = key.encode("utf-8")
    iv = "0102030405060708".encode("utf-8")

    ivLen = 16 - len(data) % 16
    data += chr(ivLen) * ivLen

    a = data.encode("utf-8")

    aes = AES.new(key=c, mode=AES.MODE_CBC, IV=iv)
    f = aes.encrypt(a)
    return str(base64.b64encode(f), encoding="utf-8")


i = "DyuyAY81ZYwcG43E"

value1 = encipher(json.dumps(i7b),
                     "0CoJUm6Qyw8W8jud")
value2 = encipher(value1, i)
print(value2)


data = {
    "params": value2,
    "encSecKey": "1b38b4ab53e28602432d1a5a3063670a4b5a862f54637031f1d2104ed7c806187ed4554d87c4655d48671d8e100c40f73a2eb47be6e92251a2d27fb41deb801158587217c5209b454cfc7ed457d04d40dc4baa634dccd681ae6cb41fddf4400e86774742417c525409a68649c2b1a55e577d0a9f2bdb44d5f5af162e9a283f0a"
}

response = requests.post(url, headers=headers, data=data)
with open("网易云评论区.json", "w", encoding="utf-8") as f:
    f.write(response.text)
print(response.text)
