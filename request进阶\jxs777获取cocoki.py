import requests

url = 'http://jxs777.icu:8000/Login/loginId'
session = requests.session()
headers = {
    "accept": "application/json, text/plain, */*",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
    "Content-Type": "application/json;"
}
data = {
    "username": "admin",
    "password": "ABCD1254510993"
}
cookie = session.post(url, headers=headers, json=data)

params = {"pageNum": 1,
          "pageSize": 5,
          "headLine": "",
          "love": "",
          "label": "",
          "startTime": "2023-12-31T16:00:00.000Z",
          "endTime": "2023-12-31T16:00:00.000Z"
          }
post = session.post("http://jxs777.icu:8000/home/<USER>", json=params)
print(post.text)
