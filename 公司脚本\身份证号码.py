import time

import pandas as pd
import logging
import re

import pymysql

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 读取Excel文件, 数据要求，第一列为案件编号，第二列为原始号码，第三列为新号码，没有列标题和大标题
df = pd.read_excel(r'C:\Users\<USER>\Desktop\修改\身份证.xlsx', engine='openpyxl', header=None)

# 初始化新的列表
newID = []
newNumber = []
oldNumber = []

if input("批量修改身份证号，是否确定？y/n") == "y":
    logging.info("开始批量修改")
    time.sleep(1)
else:
    exit()
# 遍历每一行数据
for index, row in df.iterrows():
    id = row[0].replace("\n", "").replace("'", "")
    newID.append(id)

    # 不为空
    if not pd.isna(row[1]):
        try:
            re_compile = re.compile(r"当事人身份证号：：(?P<phone>\d{17}[0-9Xx])", re.S)
            phone = re_compile.search(row[1]).group("phone")
            replace = str(phone).replace("\n", "").replace("'", "")
            oldNumber.append(replace)
        except AttributeError:
            replace = str(row[1]).replace("\n", "").replace("'", "")
            oldNumber.append(replace)
    else:
        oldNumber.append(None)

    # 不为空
    if not pd.isna(row[2]):
        try:
            re_compile = re.compile(r"当事人身份证号：(?P<phone>\d{17}[0-9Xx])", re.S)
            phone = re_compile.search(row[2]).group("phone")
            replace = str(phone).replace("\n", "").replace("'", "")
            newNumber.append(replace)
        except AttributeError:
            replace = str(row[2]).replace("\n", "").replace("'", "")
            newNumber.append(replace)
    else:
        newNumber.append(None)

time.sleep(1)
for i in range(len(newID)):
    print(f"案件编号: {newID[i]},原始身份证: {oldNumber[i]}, 新身份证: {newNumber[i]}")
print()
time.sleep(1)

# 数据库连接配置
db_config = {
    "host": "************",
    "user": "user_xzcf",
    "password": "Nmgyjj@2022",
    "database": "lcm_project",
    "port": 3306,
    "charset": "utf8"
}


def get_case_data(cursor, case_no):
    query_sql = """
    SELECT t.CASE_ID
    FROM t_push_case_info_task t
    WHERE t.case_no = %s;
    """
    cursor.execute(query_sql, (case_no))
    return cursor.fetchone()


def select_case_sources(cursor, case_id):
    select_sql = """
    SELECT LITIGANT_ID FROM case_source WHERE ID = %s
    """
    cursor.execute(select_sql, (case_id))
    return cursor.fetchone()


def update_litigant(cursor, id, number):
    selects_sql = """
    UPDATE litigant SET PERSON_ID_CARD = %s  
    WHERE ID = %s
    """
    cursor.execute(selects_sql, (number, id))


def updated_litigant(cursor, id):
    selects_sql = """
    SELECT * FROM LITIGANT
    WHERE ID = %s
    """
    cursor.execute(selects_sql, (id))
    return cursor.fetchone()


def befor_updated_litigant(cursor, id):
    selects_sql = """
    SELECT * FROM LITIGANT
    WHERE ID = %s
    """
    cursor.execute(selects_sql, (id))
    return cursor.fetchone()


try:
    with pymysql.connect(**db_config) as connection:
        with connection.cursor() as cursor:
            for i in range(len(newID)):
                case_no = newID[i]
                number = newNumber[i]

                print(f"案件编号: {newID[i]},原始身份证: {oldNumber[i]}, 新身份证: {newNumber[i]}")
                # 查原数据
                result = get_case_data(cursor, case_no)
                time.sleep(1)

                if result:
                    # 查询 case_sources
                    sources = select_case_sources(cursor, result[0])
                    time.sleep(1)

                    befor_litigant = befor_updated_litigant(cursor, sources[0])
                    logging.info(f"{case_no}更新前的 litigant 数据: {befor_litigant}")

                    update_litigant(cursor, sources[0], number)

                    litigant = updated_litigant(cursor, sources[0])
                    logging.info(f"{case_no}更新后的 litigant 数据: {litigant}")

                else:
                    logging.warning(f"未找到案件编号: {case_no}")
                time.sleep(1)
                print()

            time.sleep(2)
            if input("人工校验是否通过？y/n") == "y":
                logging.info("提交更改")
                connection.commit()
            else:
                logging.info("取消更改")
                connection.rollback()
except pymysql.MySQLError as e:
    logging.error(f"数据库操作失败: {e}")
