#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装QQ邮箱Excel下载器所需的依赖库
"""

import subprocess
import sys

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def main():
    """主函数"""
    print("🚀 开始安装QQ邮箱Excel下载器依赖库...")
    print("=" * 50)
    
    # 需要安装的包
    packages = [
        "imapclient",      # IMAP客户端
        "pandas",          # Excel处理
        "pymysql",         # MySQL连接
        "openpyxl",        # Excel文件支持
        "xlrd"             # 旧版Excel文件支持
    ]
    
    success_count = 0
    
    for package in packages:
        print(f"📦 正在安装 {package}...")
        if install_package(package):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"📊 安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("🎉 所有依赖库安装成功！")
        print("💡 现在可以运行: python qq_downloader_gui.py")
    else:
        print("⚠️ 部分依赖库安装失败，请手动安装")
        print("💡 手动安装命令:")
        for package in packages:
            print(f"   pip install {package}")

if __name__ == "__main__":
    main()
