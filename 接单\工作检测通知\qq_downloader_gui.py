#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ邮箱Excel文件下载器 - 图形化界面版
现代化设计，支持多个QQ邮箱下载Excel文件
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from email.header import decode_header
import email
import re

try:
    from imapclient import IMAPClient
except ImportError:
    messagebox.showerror("缺少依赖", "请先安装 imapclient 库:\npip install imapclient")
    exit(1)

try:
    import pandas as pd
    import pymysql
except ImportError:
    print("警告: 缺少 pandas 或 pymysql 库，Excel导入功能将不可用")
    print("请运行: pip install pandas pymysql openpyxl")
    pd = None
    pymysql = None

# ==================== 配置管理 ====================
CONFIG_FILE = "email_config.json"

DEFAULT_CONFIG = {
    "accounts": [],
    "download_folder": "downloads",
    "search_days": 30,
    "max_threads": 3,
    "database": {
        "host": "localhost",
        "port": 3306,
        "user": "root",
        "password": "",
        "database": "job_crawler"
    }
}

class QQEmailDownloaderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("QQ邮箱Excel文件下载器")
        self.root.geometry("900x700")
        self.root.configure(bg='#f0f0f0')

        # 设置窗口图标和样式
        self.setup_styles()

        # 初始化变量
        self.config = self.load_config()
        self.download_lock = threading.Lock()
        self.total_downloaded = 0
        self.is_downloading = False

        # 创建界面
        self.create_widgets()
        self.refresh_account_list()

    def setup_styles(self):
        """设置现代化样式"""
        style = ttk.Style()
        style.theme_use('clam')

        # 配置样式
        style.configure('Title.TLabel',
                       font=('Microsoft YaHei UI', 16, 'bold'),
                       background='#f0f0f0',
                       foreground='#2c3e50')

        style.configure('Heading.TLabel',
                       font=('Microsoft YaHei UI', 12, 'bold'),
                       background='#f0f0f0',
                       foreground='#34495e')

        style.configure('Info.TLabel',
                       font=('Microsoft YaHei UI', 10),
                       background='#f0f0f0',
                       foreground='#7f8c8d')

        style.configure('Success.TButton',
                       font=('Microsoft YaHei UI', 10, 'bold'),
                       foreground='white')

        style.map('Success.TButton',
                 background=[('active', '#27ae60'), ('!active', '#2ecc71')])

        style.configure('Danger.TButton',
                       font=('Microsoft YaHei UI', 10, 'bold'),
                       foreground='white')

        style.map('Danger.TButton',
                 background=[('active', '#c0392b'), ('!active', '#e74c3c')])

        style.configure('Primary.TButton',
                       font=('Microsoft YaHei UI', 10, 'bold'),
                       foreground='white')

        style.map('Primary.TButton',
                 background=[('active', '#2980b9'), ('!active', '#3498db')])

    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg='#f0f0f0', pady=20)
        title_frame.pack(fill='x')

        title_label = ttk.Label(title_frame, text="📧 QQ邮箱Excel文件下载器", style='Title.TLabel')
        title_label.pack()

        subtitle_label = ttk.Label(title_frame, text="多线程下载，高效便捷", style='Info.TLabel')
        subtitle_label.pack(pady=(5, 0))

        # 创建主容器
        main_container = tk.Frame(self.root, bg='#f0f0f0')
        main_container.pack(fill='both', expand=True, padx=20, pady=10)

        # 左侧面板 - 账号管理
        left_panel = self.create_account_panel(main_container)
        left_panel.pack(side='left', fill='both', expand=True, padx=(0, 10))

        # 右侧面板 - 设置和下载
        right_panel = self.create_control_panel(main_container)
        right_panel.pack(side='right', fill='both', expand=True, padx=(10, 0))

        # 底部状态栏
        self.create_status_bar()

    def create_account_panel(self, parent):
        """创建账号管理面板"""
        panel = tk.LabelFrame(parent, text="📋 邮箱账号管理",
                             font=('Microsoft YaHei UI', 12, 'bold'),
                             bg='white', fg='#2c3e50',
                             relief='solid', bd=1, padx=15, pady=15)

        # 账号列表
        list_frame = tk.Frame(panel, bg='white')
        list_frame.pack(fill='both', expand=True, pady=(0, 15))

        # 创建Treeview显示账号
        columns = ('序号', '邮箱地址', '状态')
        self.account_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)

        # 设置列
        self.account_tree.heading('序号', text='序号')
        self.account_tree.heading('邮箱地址', text='邮箱地址')
        self.account_tree.heading('状态', text='状态')

        self.account_tree.column('序号', width=60, anchor='center')
        self.account_tree.column('邮箱地址', width=200, anchor='w')
        self.account_tree.column('状态', width=80, anchor='center')

        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.account_tree.yview)
        self.account_tree.configure(yscrollcommand=scrollbar.set)

        self.account_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # 按钮区域
        button_frame = tk.Frame(panel, bg='white')
        button_frame.pack(fill='x')

        add_btn = ttk.Button(button_frame, text="➕ 添加账号",
                            command=self.add_account_dialog, style='Success.TButton')
        add_btn.pack(side='left', padx=(0, 10))

        remove_btn = ttk.Button(button_frame, text="🗑️ 删除账号",
                               command=self.remove_account, style='Danger.TButton')
        remove_btn.pack(side='left')

        return panel

    def create_control_panel(self, parent):
        """创建控制面板"""
        panel = tk.LabelFrame(parent, text="⚙️ 下载设置",
                             font=('Microsoft YaHei UI', 12, 'bold'),
                             bg='white', fg='#2c3e50',
                             relief='solid', bd=1, padx=15, pady=15)

        # 设置区域
        settings_frame = tk.Frame(panel, bg='white')
        settings_frame.pack(fill='x', pady=(0, 20))

        # 搜索天数
        days_frame = tk.Frame(settings_frame, bg='white')
        days_frame.pack(fill='x', pady=5)
        ttk.Label(days_frame, text="📅 搜索天数:", font=('Microsoft YaHei UI', 10)).pack(side='left')
        self.days_var = tk.StringVar(value=str(self.config.get('search_days', 30)))
        days_spinbox = ttk.Spinbox(days_frame, from_=1, to=365, width=10, textvariable=self.days_var)
        days_spinbox.pack(side='right')

        # 线程数
        threads_frame = tk.Frame(settings_frame, bg='white')
        threads_frame.pack(fill='x', pady=5)
        ttk.Label(threads_frame, text="🧵 线程数:", font=('Microsoft YaHei UI', 10)).pack(side='left')
        self.threads_var = tk.StringVar(value=str(self.config.get('max_threads', 3)))
        threads_spinbox = ttk.Spinbox(threads_frame, from_=1, to=10, width=10, textvariable=self.threads_var)
        threads_spinbox.pack(side='right')

        # 下载文件夹
        folder_frame = tk.Frame(settings_frame, bg='white')
        folder_frame.pack(fill='x', pady=5)
        ttk.Label(folder_frame, text="📁 下载目录:", font=('Microsoft YaHei UI', 10)).pack(side='left')
        self.folder_var = tk.StringVar(value=self.config.get('download_folder', 'downloads'))
        folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_var, width=20)
        folder_entry.pack(side='right', padx=(0, 5))
        folder_btn = ttk.Button(folder_frame, text="📂", width=3, command=self.select_folder)
        folder_btn.pack(side='right')

        # 下载按钮
        download_frame = tk.Frame(panel, bg='white')
        download_frame.pack(fill='x', pady=(0, 10))

        self.download_btn = ttk.Button(download_frame, text="🚀 开始下载Excel文件",
                                      command=self.start_download, style='Primary.TButton')
        self.download_btn.pack(fill='x', ipady=10)

        # Excel导入按钮区域
        import_frame = tk.Frame(panel, bg='white')
        import_frame.pack(fill='x', pady=(0, 10))

        # 数据库配置按钮
        db_config_btn = ttk.Button(import_frame, text="🔧 数据库配置",
                                  command=self.show_database_config)
        db_config_btn.pack(side='left', padx=(0, 10), ipady=5)

        # Excel导入按钮
        self.import_btn = ttk.Button(import_frame, text="📊 导入Excel到数据库",
                                    command=self.start_import_excel, style='Success.TButton')
        self.import_btn.pack(side='right', ipady=5)

        # 进度显示
        progress_frame = tk.Frame(panel, bg='white')
        progress_frame.pack(fill='x')

        ttk.Label(progress_frame, text="📊 下载进度:", font=('Microsoft YaHei UI', 10, 'bold')).pack(anchor='w')

        self.progress_var = tk.StringVar(value="等待开始...")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var,
                                       font=('Microsoft YaHei UI', 9), foreground='#7f8c8d')
        self.progress_label.pack(anchor='w', pady=(5, 10))

        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill='x')

        # 日志显示
        log_frame = tk.Frame(panel, bg='white')
        log_frame.pack(fill='both', expand=True, pady=(15, 0))

        ttk.Label(log_frame, text="📝 下载日志:", font=('Microsoft YaHei UI', 10, 'bold')).pack(anchor='w')

        # 创建日志文本框
        log_text_frame = tk.Frame(log_frame, bg='white')
        log_text_frame.pack(fill='both', expand=True, pady=(5, 0))

        self.log_text = tk.Text(log_text_frame, height=8, font=('Consolas', 9),
                               bg='#2c3e50', fg='#ecf0f1', insertbackground='white')
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side='left', fill='both', expand=True)
        log_scrollbar.pack(side='right', fill='y')

        return panel

    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(self.root, bg='#34495e', height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        self.status_var = tk.StringVar(value="就绪")
        status_label = tk.Label(status_frame, textvariable=self.status_var,
                               bg='#34495e', fg='white', font=('Microsoft YaHei UI', 9))
        status_label.pack(side='left', padx=10, pady=5)

        # 版本信息
        version_label = tk.Label(status_frame, text="v1.0.0",
                                bg='#34495e', fg='#bdc3c7', font=('Microsoft YaHei UI', 8))
        version_label.pack(side='right', padx=10, pady=5)

    def load_config(self):
        """加载配置文件"""
        if os.path.exists(CONFIG_FILE):
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return DEFAULT_CONFIG.copy()

    def save_config(self):
        """保存配置文件"""
        try:
            # 更新配置
            self.config['search_days'] = int(self.days_var.get())
            self.config['max_threads'] = int(self.threads_var.get())
            self.config['download_folder'] = self.folder_var.get()

            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False

    def refresh_account_list(self):
        """刷新账号列表"""
        # 清空现有项目
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)

        # 添加账号
        for i, account in enumerate(self.config.get('accounts', []), 1):
            self.account_tree.insert('', 'end', values=(i, account['email'], '✅ 已配置'))

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def update_progress(self, message):
        """更新进度信息"""
        self.progress_var.set(message)
        self.status_var.set(message)

    def add_account_dialog(self):
        """添加账号对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("添加QQ邮箱账号")
        dialog.geometry("450x400")  # 增加高度确保按钮可见
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.resizable(False, False)  # 禁止调整大小

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 225, self.root.winfo_rooty() + 150))

        # 标题
        title_label = tk.Label(dialog, text="📧 添加QQ邮箱账号",
                              font=('Microsoft YaHei UI', 14, 'bold'),
                              bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # 输入框架
        input_frame = tk.Frame(dialog, bg='white')
        input_frame.pack(fill='x', padx=30, pady=20)

        # 邮箱输入
        tk.Label(input_frame, text="QQ邮箱:", font=('Microsoft YaHei UI', 10),
                bg='white', fg='#34495e').pack(anchor='w', pady=(0, 5))
        email_var = tk.StringVar()
        email_entry = ttk.Entry(input_frame, textvariable=email_var, font=('Microsoft YaHei UI', 10))
        email_entry.pack(fill='x', ipady=5)

        # 授权码输入
        tk.Label(input_frame, text="授权码:", font=('Microsoft YaHei UI', 10),
                bg='white', fg='#34495e').pack(anchor='w', pady=(15, 5))
        password_var = tk.StringVar()
        password_entry = ttk.Entry(input_frame, textvariable=password_var, show='*',
                                  font=('Microsoft YaHei UI', 10))
        password_entry.pack(fill='x', ipady=5)

        # 帮助信息
        help_text = """💡 获取授权码步骤：
1. 登录QQ邮箱网页版
2. 设置 → 账户 → POP3/IMAP/SMTP服务
3. 开启IMAP/SMTP服务并生成授权码"""

        help_label = tk.Label(input_frame, text=help_text,
                             font=('Microsoft YaHei UI', 8),
                             bg='#ecf0f1', fg='#7f8c8d', justify='left',
                             relief='solid', bd=1)
        help_label.pack(fill='x', pady=(15, 0), padx=5, ipady=10)

        # 按钮区域 - 增加间距和样式
        button_frame = tk.Frame(dialog, bg='white')
        button_frame.pack(fill='x', padx=30, pady=(20, 30))

        def add_account():
            email = email_var.get().strip()
            password = password_var.get().strip()

            if not email or not email.endswith('@qq.com'):
                messagebox.showerror("错误", "请输入正确的QQ邮箱地址")
                return

            if not password:
                messagebox.showerror("错误", "请输入授权码")
                return

            # 检查是否已存在
            for account in self.config.get('accounts', []):
                if account['email'] == email:
                    messagebox.showerror("错误", "该邮箱已存在")
                    return

            # 添加账号
            if 'accounts' not in self.config:
                self.config['accounts'] = []

            self.config['accounts'].append({'email': email, 'password': password})

            if self.save_config():
                messagebox.showinfo("成功", "邮箱账号添加成功！")
                self.refresh_account_list()
                dialog.destroy()
            else:
                messagebox.showerror("错误", "保存配置失败")

        # 创建更大更明显的按钮
        cancel_btn = ttk.Button(button_frame, text="❌ 取消", command=dialog.destroy)
        cancel_btn.pack(side='right', padx=(10, 0), ipady=8)

        add_btn = ttk.Button(button_frame, text="✅ 添加账号", command=add_account, style='Success.TButton')
        add_btn.pack(side='right', ipady=8)

        # 添加回车键绑定
        def on_enter(event):
            add_account()

        dialog.bind('<Return>', on_enter)
        email_entry.bind('<Return>', on_enter)
        password_entry.bind('<Return>', on_enter)

        # 设置焦点
        email_entry.focus()

    def remove_account(self):
        """删除选中的账号"""
        selection = self.account_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要删除的账号")
            return

        item = selection[0]
        values = self.account_tree.item(item, 'values')
        email = values[1]

        if messagebox.askyesno("确认删除", f"确定要删除邮箱账号 {email} 吗？"):
            # 从配置中删除
            self.config['accounts'] = [acc for acc in self.config.get('accounts', [])
                                     if acc['email'] != email]

            if self.save_config():
                messagebox.showinfo("成功", "账号删除成功！")
                self.refresh_account_list()
            else:
                messagebox.showerror("错误", "保存配置失败")

    def select_folder(self):
        """选择下载文件夹"""
        folder = filedialog.askdirectory(title="选择下载文件夹")
        if folder:
            self.folder_var.set(folder)

    def show_database_config(self):
        """显示数据库配置对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("数据库配置")
        dialog.geometry("450x500")  # 增加高度确保所有控件可见
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.resizable(False, False)

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 225, self.root.winfo_rooty() + 100))

        # 标题
        title_label = tk.Label(dialog, text="🔧 数据库配置",
                              font=('Microsoft YaHei UI', 14, 'bold'),
                              bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # 创建滚动框架以防内容过多
        main_frame = tk.Frame(dialog, bg='white')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # 输入框架
        input_frame = tk.Frame(main_frame, bg='white')
        input_frame.pack(fill='x', pady=(0, 20))

        # 获取当前数据库配置
        db_config = self.config.get('database', DEFAULT_CONFIG['database'])

        # 主机地址
        host_label = tk.Label(input_frame, text="🌐 主机地址:", font=('Microsoft YaHei UI', 10, 'bold'),
                             bg='white', fg='#34495e')
        host_label.pack(anchor='w', pady=(0, 5))
        host_var = tk.StringVar(value=db_config.get('host', 'localhost'))
        host_entry = ttk.Entry(input_frame, textvariable=host_var, font=('Microsoft YaHei UI', 11))
        host_entry.pack(fill='x', ipady=8, pady=(0, 15))

        # 端口
        port_label = tk.Label(input_frame, text="🔌 端口:", font=('Microsoft YaHei UI', 10, 'bold'),
                             bg='white', fg='#34495e')
        port_label.pack(anchor='w', pady=(0, 5))
        port_var = tk.StringVar(value=str(db_config.get('port', 3306)))
        port_entry = ttk.Entry(input_frame, textvariable=port_var, font=('Microsoft YaHei UI', 11))
        port_entry.pack(fill='x', ipady=8, pady=(0, 15))

        # 用户名
        user_label = tk.Label(input_frame, text="👤 用户名:", font=('Microsoft YaHei UI', 10, 'bold'),
                             bg='white', fg='#34495e')
        user_label.pack(anchor='w', pady=(0, 5))
        user_var = tk.StringVar(value=db_config.get('user', 'root'))
        user_entry = ttk.Entry(input_frame, textvariable=user_var, font=('Microsoft YaHei UI', 11))
        user_entry.pack(fill='x', ipady=8, pady=(0, 15))

        # 密码
        password_label = tk.Label(input_frame, text="🔒 密码:", font=('Microsoft YaHei UI', 10, 'bold'),
                                 bg='white', fg='#34495e')
        password_label.pack(anchor='w', pady=(0, 5))
        password_var = tk.StringVar(value=db_config.get('password', ''))
        password_entry = ttk.Entry(input_frame, textvariable=password_var, show='*',
                                  font=('Microsoft YaHei UI', 11))
        password_entry.pack(fill='x', ipady=8, pady=(0, 15))

        # 数据库名
        database_label = tk.Label(input_frame, text="🗃️ 数据库名:", font=('Microsoft YaHei UI', 10, 'bold'),
                                 bg='white', fg='#34495e')
        database_label.pack(anchor='w', pady=(0, 5))
        database_var = tk.StringVar(value=db_config.get('database', 'job_crawler'))
        database_entry = ttk.Entry(input_frame, textvariable=database_var, font=('Microsoft YaHei UI', 11))
        database_entry.pack(fill='x', ipady=8, pady=(0, 15))

        # 按钮区域
        button_frame = tk.Frame(main_frame, bg='white')
        button_frame.pack(fill='x', pady=(10, 0))

        def test_connection():
            """测试数据库连接"""
            if not pymysql:
                messagebox.showerror("错误", "缺少 pymysql 库，请先安装")
                return

            try:
                conn = pymysql.connect(
                    host=host_var.get().strip(),
                    port=int(port_var.get().strip()),
                    user=user_var.get().strip(),
                    password=password_var.get(),
                    database=database_var.get().strip(),
                    charset='utf8mb4'
                )
                conn.close()
                messagebox.showinfo("成功", "数据库连接测试成功！")
            except Exception as e:
                messagebox.showerror("连接失败", f"数据库连接失败:\n{str(e)}")

        def save_db_config():
            """保存数据库配置"""
            try:
                port = int(port_var.get().strip())
            except ValueError:
                messagebox.showerror("错误", "端口必须是数字")
                return

            # 更新配置
            self.config['database'] = {
                'host': host_var.get().strip(),
                'port': port,
                'user': user_var.get().strip(),
                'password': password_var.get(),
                'database': database_var.get().strip()
            }

            if self.save_config():
                messagebox.showinfo("成功", "数据库配置保存成功！")
                dialog.destroy()
            else:
                messagebox.showerror("错误", "保存配置失败")

        # 测试连接按钮
        test_btn = ttk.Button(button_frame, text="🔍 测试连接", command=test_connection)
        test_btn.pack(side='left', ipady=8)

        # 右侧按钮组
        right_buttons = tk.Frame(button_frame, bg='white')
        right_buttons.pack(side='right')

        # 取消和保存按钮
        save_btn = ttk.Button(right_buttons, text="✅ 保存配置", command=save_db_config, style='Success.TButton')
        save_btn.pack(side='right', padx=(10, 0), ipady=8)

        cancel_btn = ttk.Button(right_buttons, text="❌ 取消", command=dialog.destroy)
        cancel_btn.pack(side='right', ipady=8)

        # 添加键盘快捷键
        def on_enter(event):
            save_db_config()

        def on_escape(event):
            dialog.destroy()

        # 绑定快捷键
        dialog.bind('<Return>', on_enter)
        dialog.bind('<Escape>', on_escape)

        # 为所有输入框绑定回车键
        for entry in [host_entry, port_entry, user_entry, password_entry, database_entry]:
            entry.bind('<Return>', on_enter)

        # 设置焦点
        host_entry.focus()
        host_entry.select_range(0, tk.END)  # 选中所有文本便于修改

    def start_import_excel(self):
        """开始导入Excel文件到数据库"""
        if not pd or not pymysql:
            messagebox.showerror("错误", "缺少必要的库，请安装:\npip install pandas pymysql openpyxl")
            return

        if self.is_downloading:
            messagebox.showwarning("提示", "正在下载中，请等待完成后再导入")
            return

        # 检查数据库配置
        db_config = self.config.get('database')
        if not db_config:
            messagebox.showwarning("提示", "请先配置数据库连接")
            return

        # 确认导入
        download_folder = Path(self.config['download_folder'])
        if not download_folder.exists():
            messagebox.showwarning("提示", f"下载文件夹不存在: {download_folder}")
            return

        if not messagebox.askyesno("确认导入",
                                  f"将扫描文件夹 {download_folder} 中的所有Excel文件\n"
                                  f"并导入到数据库 {db_config['database']}\n"
                                  f"确认开始导入吗？"):
            return

        # 清空日志
        self.log_text.delete(1.0, tk.END)

        # 开始导入
        self.import_btn.configure(text="⏸️ 导入中...", state='disabled')
        self.progress_bar.start()

        # 在新线程中执行导入
        import_thread = threading.Thread(target=self.import_excel_worker)
        import_thread.daemon = True
        import_thread.start()

    def import_excel_worker(self):
        """Excel导入工作线程"""
        try:
            self.log_message("📊 开始扫描Excel文件...")

            download_folder = Path(self.config['download_folder'])
            excel_files = []

            # 扫描所有Excel文件
            for ext in ['*.xlsx', '*.xls', '*.xlsm', '*.xlsb']:
                excel_files.extend(download_folder.rglob(ext))

            self.log_message(f"📁 找到 {len(excel_files)} 个Excel文件")

            if not excel_files:
                self.log_message("❌ 没有找到Excel文件")
                self.root.after(0, self.import_completed)
                return

            # 连接数据库
            db_config = self.config['database']
            try:
                conn = pymysql.connect(
                    host=db_config['host'],
                    port=db_config['port'],
                    user=db_config['user'],
                    password=db_config['password'],
                    database=db_config['database'],
                    charset='utf8mb4'
                )
                self.log_message("✅ 数据库连接成功")
            except Exception as e:
                self.log_message(f"❌ 数据库连接失败: {e}")
                self.root.after(0, self.import_completed)
                return

            cursor = conn.cursor()
            total_imported = 0

            # 处理每个Excel文件
            for i, excel_file in enumerate(excel_files, 1):
                self.log_message(f"📄 [{i}/{len(excel_files)}] 处理文件: {excel_file.name}")

                retry_count = 0
                max_retries = 3

                while retry_count < max_retries:
                    try:
                        # 读取Excel文件，保留超链接信息
                        df = pd.read_excel(excel_file, engine='openpyxl')

                        if df.empty:
                            self.log_message(f"    ⚠️ 文件为空，跳过")
                            break

                        # 检查列数，至少要有基本字段
                        if len(df.columns) < 4:
                            self.log_message(f"    ⚠️ 列数不足，跳过")
                            break

                        # 跳过表头行，从第二行开始处理数据
                        # 检查第一行是否为表头（包含"公司名称"、"岗位名称"等关键词）
                        first_row = df.iloc[0] if len(df) > 0 else None
                        start_row = 0

                        if first_row is not None:
                            first_row_str = ' '.join([str(cell) for cell in first_row if pd.notna(cell)])
                            if any(keyword in first_row_str for keyword in ['公司名称', '岗位名称', '工作地点', '学历要求']):
                                start_row = 1  # 跳过表头行
                                self.log_message(f"    📋 检测到表头，从第{start_row + 1}行开始导入")

                        # 读取Excel工作簿以获取超链接信息
                        try:
                            from openpyxl import load_workbook
                            wb = load_workbook(excel_file)
                            ws = wb.active
                            hyperlinks = {}

                            # 提取所有超链接
                            for row in ws.iter_rows():
                                for cell in row:
                                    if cell.hyperlink:
                                        hyperlinks[f"{cell.row}_{cell.column}"] = cell.hyperlink.target
                        except:
                            hyperlinks = {}

                        # 处理每一行数据（跳过表头）
                        imported_rows = 0
                        for idx, row in df.iterrows():
                            if idx < start_row:  # 跳过表头行
                                continue
                            try:
                                # 映射Excel列到数据库字段，传入超链接信息
                                data = self.map_excel_to_db(row, idx + 2, hyperlinks)  # idx+2因为Excel行号从1开始，且可能跳过了表头

                                # 调试信息：显示Excel行的详细内容
                                if imported_rows < 3:  # 只显示前3行的详细信息
                                    self.log_message(f"    🔍 第{idx+1}行数据调试:")
                                    self.log_message(f"        Excel列数: {len(row)}")
                                    for i in range(min(13, len(row))):
                                        col_value = row.iloc[i] if i < len(row) else "N/A"
                                        self.log_message(f"        列{i}: '{col_value}' (类型: {type(col_value)})")

                                # 检查岗位职责和岗位要求是否为空
                                position_desc = data[10]  # 岗位职责
                                position_requirement = data[11]  # 岗位要求

                                if not position_desc or position_desc.strip() == '':
                                    self.log_message(f"    ⚠️ 第{idx+1}行岗位职责为空: '{position_desc}'")
                                if not position_requirement or position_requirement.strip() == '':
                                    self.log_message(f"    ⚠️ 第{idx+1}行岗位要求为空: '{position_requirement}'")

                                # 插入数据库，显式添加系统时间
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                sql = """
                                INSERT INTO jobs (
                                    company_name, company_type, industry, position_name,
                                    work_location, education_req, major_req, post_date,
                                    end_date, position_link, position_desc, position_requirement,
                                    backup_link, created_at, updated_at
                                ) VALUES (
                                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                                )
                                """

                                # 添加系统时间到数据中
                                data_with_time = data + (current_time, current_time)
                                cursor.execute(sql, data_with_time)
                                imported_rows += 1

                            except Exception as e:
                                # 单行错误不影响整体导入
                                continue

                        conn.commit()
                        total_imported += imported_rows
                        self.log_message(f"    ✅ 成功导入 {imported_rows} 条记录")
                        break  # 成功处理，跳出重试循环

                    except Exception as e:
                        retry_count += 1
                        if retry_count < max_retries:
                            self.log_message(f"    ⚠️ 读取失败，重试 {retry_count}/{max_retries}: {e}")
                        else:
                            self.log_message(f"    ❌ 读取失败，跳过文件: {e}")

            conn.close()

            self.log_message("=" * 50)
            self.log_message(f"🎉 Excel导入完成！")
            self.log_message(f"📊 总共导入: {total_imported} 条记录")
            self.log_message(f"📁 处理文件: {len(excel_files)} 个")

            # 更新UI
            self.root.after(0, lambda: self.import_completed(total_imported))

        except Exception as e:
            self.log_message(f"❌ 导入过程出错: {e}")
            self.root.after(0, self.import_completed)

    def map_excel_to_db(self, row, excel_row_num, hyperlinks=None):
        """映射Excel行数据到数据库字段"""
        # Excel字段顺序：公司名称、公司类型、行业、岗位名称、工作地点、学历要求、专业要求、
        # 发布时间、截止时间、岗位链接、岗位职责、岗位要求、备用链接

        def safe_get(index, default=''):
            try:
                if index >= len(row):
                    return default
                value = row.iloc[index]
                if pd.isna(value) or value is None:
                    return default
                str_value = str(value).strip()
                if str_value.lower() in ['nan', 'none', '']:
                    return default
                return str_value
            except:
                return default

        def get_hyperlink(index, default=''):
            """获取指定列的超链接，如果没有超链接则返回单元格文本"""
            if hyperlinks:
                # Excel列号从1开始，A=1, B=2, ...
                hyperlink_key = f"{excel_row_num}_{index + 1}"
                if hyperlink_key in hyperlinks:
                    return hyperlinks[hyperlink_key]

            # 如果没有超链接，返回单元格文本
            return safe_get(index, default)

        return (
            safe_get(0),        # company_name - 公司名称
            safe_get(1),        # company_type - 公司类型
            safe_get(2),        # industry - 行业
            safe_get(3),        # position_name - 岗位名称
            safe_get(4),        # work_location - 工作地点
            safe_get(5),        # education_req - 学历要求
            safe_get(6),        # major_req - 专业要求
            safe_get(7),        # post_date - 发布时间
            safe_get(8),        # end_date - 截止时间
            get_hyperlink(9),   # position_link - 岗位链接（提取超链接）
            safe_get(10),       # position_desc - 岗位职责
            safe_get(11),       # position_requirement - 岗位要求
            get_hyperlink(12)   # backup_link - 备用链接（提取超链接）
        )

    def import_completed(self, total_imported=0):
        """导入完成后的UI更新"""
        self.import_btn.configure(text="📊 导入Excel到数据库", state='normal')
        self.progress_bar.stop()
        self.update_progress(f"导入完成，共导入 {total_imported} 条记录")

        # 显示完成消息
        if total_imported > 0:
            messagebox.showinfo("导入完成",
                               f"Excel导入完成！\n共导入 {total_imported} 条记录到数据库")
        else:
            messagebox.showwarning("导入完成", "没有导入任何记录，请检查Excel文件格式")

    def start_download(self):
        """开始下载"""
        if self.is_downloading:
            messagebox.showwarning("提示", "正在下载中，请等待完成")
            return

        if not self.config.get('accounts'):
            messagebox.showwarning("提示", "请先添加邮箱账号")
            return

        # 保存当前设置
        if not self.save_config():
            messagebox.showerror("错误", "保存配置失败")
            return

        # 确认开始下载
        account_count = len(self.config['accounts'])
        if not messagebox.askyesno("确认下载",
                                  f"将从 {account_count} 个邮箱账号下载Excel文件\n"
                                  f"搜索范围：最近 {self.config['search_days']} 天\n"
                                  f"确认开始下载吗？"):
            return

        # 清空日志
        self.log_text.delete(1.0, tk.END)

        # 开始下载
        self.is_downloading = True
        self.download_btn.configure(text="⏸️ 下载中...", state='disabled')
        self.progress_bar.start()

        # 在新线程中执行下载
        download_thread = threading.Thread(target=self.download_worker)
        download_thread.daemon = True
        download_thread.start()

    def download_worker(self):
        """下载工作线程"""
        try:
            self.total_downloaded = 0

            self.log_message("🚀 开始多线程下载...")
            self.update_progress("正在初始化...")

            # 创建下载文件夹
            download_folder = Path(self.config['download_folder'])
            download_folder.mkdir(parents=True, exist_ok=True)

            self.log_message(f"📁 下载目录: {download_folder.absolute()}")
            self.log_message(f"📧 邮箱账号: {len(self.config['accounts'])} 个")
            self.log_message(f"🧵 最大线程数: {self.config['max_threads']}")

            start_time = time.time()

            # 使用线程池执行下载
            with ThreadPoolExecutor(max_workers=self.config['max_threads']) as executor:
                # 提交所有下载任务
                future_to_account = {
                    executor.submit(self.download_from_single_account, account): account
                    for account in self.config['accounts']
                }

                # 等待所有任务完成
                for future in as_completed(future_to_account):
                    account = future_to_account[future]
                    try:
                        result = future.result()
                    except Exception as e:
                        self.log_message(f"❌ [{account['email']}] 任务异常: {e}")

            end_time = time.time()
            elapsed_time = end_time - start_time

            self.log_message("=" * 50)
            self.log_message(f"🎉 所有下载任务完成！")
            self.log_message(f"📊 总共下载: {self.total_downloaded} 个Excel文件")
            self.log_message(f"⏱️  用时: {elapsed_time:.2f} 秒")
            self.log_message(f"📁 文件保存在: {download_folder.absolute()}")

            # 更新UI
            self.root.after(0, self.download_completed)

        except Exception as e:
            self.log_message(f"❌ 下载过程出错: {e}")
            self.root.after(0, self.download_completed)

    def download_from_single_account(self, account):
        """从单个邮箱账号下载Excel文件"""
        email_addr = account["email"]
        password = account["password"]
        download_folder = Path(self.config["download_folder"])
        search_days = self.config["search_days"]

        downloaded_count = 0

        try:
            self.log_message(f"🔗 [{email_addr}] 正在连接...")

            # 使用imapclient连接QQ邮箱
            with IMAPClient(host='imap.qq.com', port=993, ssl=True) as client:
                client.login(email_addr, password)
                client.select_folder('INBOX')

                self.log_message(f"✅ [{email_addr}] 连接成功")

                # 构建搜索条件
                since_date = (datetime.now() - timedelta(days=search_days)).strftime("%d-%b-%Y")
                search_criteria = ['SINCE', since_date]

                # 搜索邮件
                messages = client.search(search_criteria)
                self.log_message(f"📧 [{email_addr}] 找到 {len(messages)} 封邮件")

                # 创建邮箱专用文件夹
                email_prefix = email_addr.split('@')[0]
                account_folder = download_folder / email_prefix
                account_folder.mkdir(parents=True, exist_ok=True)

                # 处理每封邮件
                for i, msg_id in enumerate(messages, 1):
                    try:
                        # 获取邮件数据
                        msg_data = client.fetch([msg_id], ['RFC822'])[msg_id]
                        raw_email = msg_data[b'RFC822']
                        parsed_email = email.message_from_bytes(raw_email)

                        # 处理附件
                        for part in parsed_email.walk():
                            if part.get_content_maintype() == 'multipart':
                                continue

                            content_disposition = part.get('Content-Disposition')
                            if content_disposition is None:
                                continue

                            filename = part.get_filename()
                            if not filename:
                                continue

                            # 解码文件名
                            decoded_filename = self.decode_filename(filename)

                            # 检查是否为Excel文件
                            if self.is_excel_file(decoded_filename):
                                # 生成安全的文件名
                                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
                                safe_filename = self.make_safe_filename(decoded_filename)
                                final_filename = f"{timestamp}_{safe_filename}"

                                # 保存文件
                                save_path = account_folder / final_filename

                                try:
                                    with open(save_path, 'wb') as f:
                                        f.write(part.get_payload(decode=True))

                                    downloaded_count += 1
                                    with self.download_lock:
                                        self.total_downloaded += 1

                                    self.log_message(f"📥 [{email_addr}] 下载: {decoded_filename}")

                                except Exception as e:
                                    self.log_message(f"⚠️ [{email_addr}] 保存文件失败: {e}")

                    except Exception as e:
                        continue

                self.log_message(f"✅ [{email_addr}] 完成，下载了 {downloaded_count} 个文件")
                return downloaded_count

        except Exception as e:
            self.log_message(f"❌ [{email_addr}] 连接错误: {e}")
            return 0

    def download_completed(self):
        """下载完成后的UI更新"""
        self.is_downloading = False
        self.download_btn.configure(text="🚀 开始下载Excel文件", state='normal')
        self.progress_bar.stop()
        self.update_progress(f"下载完成，共下载 {self.total_downloaded} 个文件")

        # 显示完成消息
        messagebox.showinfo("下载完成",
                           f"下载完成！\n共下载 {self.total_downloaded} 个Excel文件\n"
                           f"文件保存在: {self.config['download_folder']}")

    # ==================== 辅助函数 ====================

    def decode_filename(self, encoded_name):
        """解码文件名"""
        if not encoded_name:
            return ""

        try:
            decoded = decode_header(encoded_name)[0]
            if isinstance(decoded[0], bytes):
                encodings = [decoded[1], 'utf-8', 'gbk', 'gb2312', 'latin-1']
                for encoding in encodings:
                    if encoding:
                        try:
                            return decoded[0].decode(encoding)
                        except (UnicodeDecodeError, LookupError):
                            continue
                return decoded[0].decode('utf-8', errors='ignore')
            return decoded[0]
        except Exception:
            return encoded_name

    def is_excel_file(self, filename):
        """判断是否为Excel文件"""
        excel_extensions = ['.xlsx', '.xls', '.xlsm', '.xlsb']
        return any(filename.lower().endswith(ext) for ext in excel_extensions)

    def make_safe_filename(self, filename):
        """生成安全的文件名"""
        illegal_chars = r'[<>:"/\\|?*]'
        safe_name = re.sub(illegal_chars, '_', filename)

        if len(safe_name) > 100:
            safe_name = safe_name[:100]

        safe_name = safe_name.strip('. ')
        return safe_name or "unnamed"

# ==================== 主程序 ====================

def main():
    """主函数"""
    root = tk.Tk()
    app = QQEmailDownloaderGUI(root)

    # 设置窗口关闭事件
    def on_closing():
        if app.is_downloading:
            if messagebox.askokcancel("退出", "正在下载中，确定要退出吗？"):
                root.destroy()
        else:
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # 启动GUI
    root.mainloop()

if __name__ == "__main__":
    main()