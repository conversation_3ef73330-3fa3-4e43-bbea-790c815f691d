import requests
import time

# 请求地址
serch_url = "https://u6.y.qq.com/cgi-bin/musics.fcg"
# 获取时间戳
data_time = time.time()
timestamp = int(round(data_time, 3) * 1000)
print(timestamp)
# 请求参数
params = {
    "_": f"{timestamp}",
    "sign": "zzcdca40d6xuhco4twadw2wkv2j8k6iaqm6w04856951"
}
headers = {
    "accept": "application/json",
    "accept-language": "en-GB,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,en-US;q=0.6",
    "cache-control": "no-cache",
    "content-type": "application/x-www-form-urlencoded",
    "origin": "https://y.qq.com",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "referer": "https://y.qq.com/",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"99\", \"Microsoft Edge\";v=\"127\", \"Chromium\";v=\"127\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-site",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36 Edg/127.0.0.0"
}
serch_response = requests.get(url=serch_url, params=params, headers=headers)
print(serch_response.json())
