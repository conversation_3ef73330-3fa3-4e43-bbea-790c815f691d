import requests
import csv

# 获取小说id
'''
https://dushu.baidu.com/api/pc/getSearch?data={"word":"斗破苍穹","pageNum":1}
https://dushu.baidu.com/api/pc/getCatalog?data={"book_id":"{book_id}"}
'''

handlers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36 Edg/127.0.0.0",
}
name = input("请输入小说名：")
# 搜索小说
search_url = f"https://dushu.baidu.com/api/pc/getSearch?data={{\"word\":\"{name}\",\"pageNum\":1}}"
print("书名url:" + search_url)
# 请求，获取小说id
book_response = requests.get(search_url, headers=handlers)
# 小说id
book_id = book_response.json()["data"]["list"][0]["book_id"]
# 小说标题
title = book_response.json()["data"]["list"][0]["title"]

# 拼接章节url
chapter_url = f'https://dushu.baidu.com/api/pc/getCatalog?data={{"book_id":"{book_id}"}}'
print("章节url:" + chapter_url)
# 请求，获取文章
article_list = requests.get(chapter_url, headers=handlers)
# 章节列表
items = article_list.json()["data"]["novel"]["items"]

# 打开CSV文件，并写入章节标题和内容
with open(f'{title}.csv', mode='w', newline='', encoding='utf-8') as file:
    writer = csv.writer(file)
    # 写入CSV表头
    writer.writerow(['章节标题', '内容'])

    # 遍历文章列表
    for i in items:
        # 文章url
        article_url = f'https://dushu.baidu.com/api/pc/getChapterContent?data={{"book_id":"{book_id}","cid":"{book_id}|{i["cid"]}","need_bookinfo":1}}'
        # 请求，获取文章详细内容
        article_response = requests.get(article_url, headers=handlers)
        article_response_json = article_response.json()["data"]["novel"]

        # 如果章节内容长度大于1000字，保存到CSV
        if len(article_response_json["content"]) > 1000:
            chapter_title = article_response_json["chapter_title"]
            content = article_response_json["content"]
            # 写入章节标题和内容
            writer.writerow([chapter_title, content])
        else:
            break
# 关闭链接
book_response.close()
article_list.close()
article_response.close()

print(f"小说《{title}》已保存为CSV文件！")
