#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ邮箱Excel文件下载器 - 简化版
直接运行即可使用
"""

import imaplib
import email
import os
import re
from datetime import datetime

# ==================== 配置区域 ====================
# 请修改以下配置
QQ_EMAIL = "<EMAIL>"  # 你的QQ邮箱
QQ_PASSWORD = "aodxdcnxkclqdbij"  # QQ邮箱授权码（不是QQ密码！）

# 下载设置
DOWNLOAD_FOLDER = "downloads"  # 下载文件夹
SEARCH_DAYS = 300  # 搜索最近多少天的邮件

# ==================== 主程序 ====================

def download_excel_from_qq():
    """从QQ邮箱下载Excel文件"""
    
    # 检查配置
    if QQ_EMAIL == "<EMAIL>" or QQ_PASSWORD == "your_authorization_code":
        print("❌ 请先修改脚本中的QQ邮箱和授权码配置！")
        print("💡 获取授权码方法：")
        print("   1. 登录QQ邮箱网页版")
        print("   2. 设置 → 账户 → POP3/IMAP/SMTP服务")
        print("   3. 开启IMAP/SMTP服务并生成授权码")
        return
    
    # 创建下载文件夹
    if not os.path.exists(DOWNLOAD_FOLDER):
        os.makedirs(DOWNLOAD_FOLDER)
        print(f"📁 创建下载文件夹: {DOWNLOAD_FOLDER}")
    
    try:
        # 连接QQ邮箱
        print("🔗 正在连接QQ邮箱...")
        mail = imaplib.IMAP4_SSL('imap.qq.com')
        mail.login(QQ_EMAIL, QQ_PASSWORD)
        print("✅ 邮箱连接成功")
        
        # 选择收件箱
        mail.select('INBOX')
        
        # 搜索邮件
        from datetime import datetime, timedelta
        since_date = (datetime.now() - timedelta(days=SEARCH_DAYS)).strftime("%d-%b-%Y")
        status, messages = mail.search(None, f'SINCE {since_date}')
        
        if status != 'OK':
            print("❌ 搜索邮件失败")
            return
        
        email_ids = messages[0].split()
        print(f"📧 找到 {len(email_ids)} 封邮件，正在检查Excel附件...")
        
        downloaded_count = 0
        
        # 处理每封邮件
        for i, email_id in enumerate(email_ids, 1):
            try:
                print(f"🔍 检查邮件 {i}/{len(email_ids)}", end='\r')
                
                # 获取邮件
                status, msg_data = mail.fetch(email_id, '(RFC822)')
                if status != 'OK':
                    continue
                
                # 解析邮件
                email_message = email.message_from_bytes(msg_data[0][1])
                
                # 获取邮件信息
                subject = email_message['Subject'] or '无主题'
                if subject:
                    subject = email.header.decode_header(subject)[0][0]
                    if isinstance(subject, bytes):
                        subject = subject.decode('utf-8', errors='ignore')
                
                sender = email_message['From'] or '未知发件人'
                if sender:
                    sender = email.header.decode_header(sender)[0][0]
                    if isinstance(sender, bytes):
                        sender = sender.decode('utf-8', errors='ignore')
                
                # 检查附件
                has_excel = False
                for part in email_message.walk():
                    if part.get_content_disposition() == 'attachment':
                        filename = part.get_filename()
                        if filename:
                            # 解码文件名
                            if isinstance(filename, str):
                                pass
                            else:
                                decoded = email.header.decode_header(filename)[0]
                                filename = decoded[0]
                                if isinstance(filename, bytes):
                                    filename = filename.decode(decoded[1] or 'utf-8', errors='ignore')
                            
                            # 检查是否为Excel文件
                            if filename.lower().endswith(('.xlsx', '.xls', '.xlsm', '.xlsb','')):
                                has_excel = True
                                
                                # 创建邮件文件夹
                                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                                email_folder = os.path.join(DOWNLOAD_FOLDER, f"邮件_{email_id.decode()}_{timestamp}")
                                if not os.path.exists(email_folder):
                                    os.makedirs(email_folder)
                                
                                # 清理文件名
                                clean_filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
                                filepath = os.path.join(email_folder, clean_filename)
                                
                                # 保存附件
                                with open(filepath, 'wb') as f:
                                    f.write(part.get_payload(decode=True))
                                
                                # 保存邮件信息
                                info_file = os.path.join(email_folder, '邮件信息.txt')
                                with open(info_file, 'w', encoding='utf-8') as f:
                                    f.write(f"主题: {subject}\n")
                                    f.write(f"发件人: {sender}\n")
                                    f.write(f"日期: {email_message['Date']}\n")
                                    f.write(f"附件: {clean_filename}\n")
                                
                                downloaded_count += 1
                                print(f"\n📥 下载完成: {clean_filename}")
                                print(f"   主题: {subject}")
                                print(f"   发件人: {sender}")
                
            except Exception as e:
                print(f"\n⚠️  处理邮件时出错: {e}")
                continue
        
        # 关闭连接
        mail.close()
        mail.logout()
        
        print(f"\n🎉 下载完成！")
        print(f"📊 共下载 {downloaded_count} 个Excel文件")
        print(f"📁 文件保存在: {os.path.abspath(DOWNLOAD_FOLDER)}")
        
    except imaplib.IMAP4.error as e:
        print(f"❌ 邮箱连接错误: {e}")
        print("💡 请检查:")
        print("   - QQ邮箱是否开启了IMAP服务")
        print("   - 授权码是否正确（不是QQ密码）")
    except Exception as e:
        print(f"❌ 程序运行错误: {e}")

def main():
    """主函数"""
    print("QQ邮箱Excel文件下载器")
    print("=" * 40)
    print(f"📧 邮箱: {QQ_EMAIL}")
    print(f"📅 搜索范围: 最近{SEARCH_DAYS}天")
    print(f"📁 下载目录: {DOWNLOAD_FOLDER}")
    print("-" * 40)
    
    # 确认运行
    confirm = input("确认开始下载？(y/n): ").strip().lower()
    if confirm in ['y', 'yes', '是', '确认']:
        download_excel_from_qq()
    else:
        print("❌ 取消下载")

if __name__ == "__main__":
    main()
