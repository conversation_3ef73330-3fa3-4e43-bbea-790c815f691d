#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ邮箱Excel文件下载器 - 多线程版
支持多个QQ邮箱同时下载Excel文件
"""

import imaplib
import email
import os
import re
import json
import threading
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# ==================== 配置管理 ====================
CONFIG_FILE = "email_config.json"  # 配置文件名

# 默认配置
DEFAULT_CONFIG = {
    "accounts": [],  # 多个邮箱账号列表
    "download_folder": "downloads",
    "search_days": 30,
    "max_threads": 3  # 最大线程数
}

def load_config():
    """加载配置文件"""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)

                # 兼容旧版本配置文件
                if "qq_email" in config and "qq_password" in config:
                    # 转换旧格式到新格式
                    old_config = config.copy()
                    config = DEFAULT_CONFIG.copy()

                    if old_config.get("qq_email") and old_config.get("qq_password"):
                        config["accounts"] = [{
                            "email": old_config["qq_email"],
                            "password": old_config["qq_password"]
                        }]

                    if "download_folder" in old_config:
                        config["download_folder"] = old_config["download_folder"]
                    if "search_days" in old_config:
                        config["search_days"] = old_config["search_days"]

                    # 保存新格式
                    save_config(config)
                    print("✅ 配置文件已自动升级到新格式")

                # 确保所有必需的键都存在
                for key, value in DEFAULT_CONFIG.items():
                    if key not in config:
                        config[key] = value

                return config
        except Exception as e:
            print(f"⚠️ 配置文件读取错误: {e}")
            pass

    return DEFAULT_CONFIG.copy()

def save_config(config):
    """保存配置文件"""
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except:
        return False

def add_account():
    """添加邮箱账号"""
    print("\n📝 添加QQ邮箱账号")
    print("=" * 40)

    config = load_config()

    # 输入邮箱信息
    email = input("请输入QQ邮箱: ").strip()
    if not email:
        print("❌ 邮箱不能为空")
        return False

    if not email.endswith("@qq.com"):
        print("❌ 请输入正确的QQ邮箱（必须以@qq.com结尾）")
        return False

    # 检查是否已存在
    for account in config["accounts"]:
        if account["email"] == email:
            print("❌ 该邮箱已存在")
            return False

    password = input("请输入QQ邮箱授权码: ").strip()
    if not password:
        print("❌ 授权码不能为空")
        return False

    # 添加账号
    config["accounts"].append({
        "email": email,
        "password": password
    })

    if save_config(config):
        print("✅ 邮箱账号添加成功！")
        return True
    else:
        print("❌ 保存失败")
        return False

def remove_account():
    """删除邮箱账号"""
    config = load_config()

    if not config["accounts"]:
        print("❌ 没有配置的邮箱账号")
        return False

    print("\n📝 删除邮箱账号")
    print("=" * 40)

    # 显示所有账号
    for i, account in enumerate(config["accounts"], 1):
        print(f"{i}. {account['email']}")

    try:
        choice = int(input("\n请选择要删除的账号序号: ").strip())
        if 1 <= choice <= len(config["accounts"]):
            removed = config["accounts"].pop(choice - 1)
            if save_config(config):
                print(f"✅ 已删除邮箱: {removed['email']}")
                return True
            else:
                print("❌ 保存失败")
                return False
        else:
            print("❌ 无效的序号")
            return False
    except ValueError:
        print("❌ 请输入有效的数字")
        return False

def update_settings():
    """更新其他设置"""
    print("\n📝 更新设置")
    print("=" * 40)

    config = load_config()

    # 搜索天数
    days = input(f"搜索天数范围 (当前{config['search_days']}天): ").strip()
    if days and days.isdigit():
        config["search_days"] = int(days)

    # 下载文件夹
    folder = input(f"下载文件夹 (当前{config['download_folder']}): ").strip()
    if folder:
        config["download_folder"] = folder

    # 最大线程数
    threads = input(f"最大线程数 (当前{config['max_threads']}): ").strip()
    if threads and threads.isdigit():
        config["max_threads"] = int(threads)

    if save_config(config):
        print("✅ 设置更新成功！")
        return True
    else:
        print("❌ 保存失败")
        return False

# ==================== 下载功能 ====================

# 线程锁和计数器
download_lock = threading.Lock()
total_downloaded = 0

def download_from_single_account(account, config):
    """从单个邮箱账号下载Excel文件"""
    global total_downloaded

    email_addr = account["email"]
    password = account["password"]
    download_folder = config["download_folder"]
    search_days = config["search_days"]

    downloaded_count = 0

    try:
        print(f"🔗 [{email_addr}] 正在连接...")

        # 连接QQ邮箱
        mail = imaplib.IMAP4_SSL('imap.qq.com')
        mail.login(email_addr, password)

        print(f"✅ [{email_addr}] 连接成功")

        # 选择收件箱
        mail.select('INBOX')

        # 搜索邮件
        from datetime import datetime, timedelta
        since_date = (datetime.now() - timedelta(days=search_days)).strftime("%d-%b-%Y")
        status, messages = mail.search(None, f'SINCE {since_date}')

        if status != 'OK':
            print(f"❌ [{email_addr}] 搜索邮件失败")
            return 0

        email_ids = messages[0].split()
        print(f"📧 [{email_addr}] 找到 {len(email_ids)} 封邮件")

        # 处理每封邮件
        for i, email_id in enumerate(email_ids, 1):
            try:
                # 获取邮件
                status, msg_data = mail.fetch(email_id, '(RFC822)')
                if status != 'OK':
                    continue

                # 解析邮件 - 使用更好的解析方法
                raw_email = msg_data[0][1]
                if isinstance(raw_email, bytes):
                    email_message = email.message_from_bytes(raw_email)
                else:
                    email_message = email.message_from_string(raw_email)

                # 获取邮件基本信息用于调试
                subject = email_message.get('Subject', '无主题')
                if subject:
                    try:
                        decoded_subject = email.header.decode_header(subject)[0][0]
                        if isinstance(decoded_subject, bytes):
                            decoded_subject = decoded_subject.decode('utf-8', errors='ignore')
                        subject = decoded_subject
                    except:
                        pass

                print(f"🔍 [{email_addr}] 处理邮件 {i}/{len(email_ids)}: {subject[:50]}...")

                # 检查附件 - 使用更直接的方法
                has_any_attachment = False

                # 方法1: 使用email.message.walk()
                for part in email_message.walk():
                    try:
                        # 跳过非邮件对象
                        if not hasattr(part, 'get_content_disposition'):
                            continue

                        # 跳过字节或字符串对象
                        if isinstance(part, (bytes, str)):
                            continue

                        # 检查多种可能的附件标识
                        content_disposition = part.get_content_disposition()
                        content_type = part.get_content_type()
                        filename = part.get_filename()

                        # 调试信息：显示所有part的信息
                        if filename or content_disposition or content_type.startswith('application/'):
                            # 解码文件名用于显示
                            display_filename = filename
                            if filename:
                                try:
                                    decoded_parts = email.header.decode_header(filename)
                                    display_filename = ''
                                    for part, encoding in decoded_parts:
                                        if isinstance(part, bytes):
                                            if encoding:
                                                display_filename += part.decode(encoding, errors='ignore')
                                            else:
                                                display_filename += part.decode('utf-8', errors='ignore')
                                        else:
                                            display_filename += part
                                except:
                                    display_filename = filename

                            print(f"    📎 Part: 文件名={display_filename}, 类型={content_type}, 处置={content_disposition}")
                            has_any_attachment = True

                        # 更宽松的附件检测条件
                        is_attachment = (
                            content_disposition == 'attachment' or
                            (filename and content_disposition in ['attachment', 'inline']) or
                            (filename and content_type.startswith('application/')) or
                            (filename and 'excel' in content_type.lower()) or
                            (filename and content_type in [
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                'application/vnd.ms-excel',
                                'application/msexcel'
                            ])
                        )

                        if is_attachment and filename:
                            try:
                                # 解码文件名 - 改进版本
                                decoded_filename = filename
                                if isinstance(filename, str):
                                    # 如果是字符串，可能包含编码的部分，需要解码
                                    try:
                                        decoded_parts = email.header.decode_header(filename)
                                        decoded_filename = ''
                                        for part, encoding in decoded_parts:
                                            if isinstance(part, bytes):
                                                if encoding:
                                                    decoded_filename += part.decode(encoding, errors='ignore')
                                                else:
                                                    decoded_filename += part.decode('utf-8', errors='ignore')
                                            else:
                                                decoded_filename += part
                                    except:
                                        decoded_filename = filename
                                else:
                                    # 如果是bytes，直接解码
                                    try:
                                        decoded_parts = email.header.decode_header(filename)
                                        decoded_filename = ''
                                        for part, encoding in decoded_parts:
                                            if isinstance(part, bytes):
                                                if encoding:
                                                    decoded_filename += part.decode(encoding, errors='ignore')
                                                else:
                                                    decoded_filename += part.decode('utf-8', errors='ignore')
                                            else:
                                                decoded_filename += part
                                    except:
                                        if isinstance(filename, bytes):
                                            decoded_filename = filename.decode('utf-8', errors='ignore')
                                        else:
                                            decoded_filename = str(filename)

                                print(f"🔍 [{email_addr}] 发现附件: {decoded_filename} (类型: {content_type})")

                                # 检查是否为Excel文件 - 主要依据文件扩展名
                                is_excel_file = (
                                    decoded_filename.lower().endswith(('.xlsx', '.xls', '.xlsm', '.xlsb')) or
                                    'excel' in content_type.lower() or
                                    content_type in [
                                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                        'application/vnd.ms-excel',
                                        'application/msexcel'
                                    ]
                                )

                                if is_excel_file:
                                    print(f"    ✅ 确认为Excel文件: {decoded_filename}")

                                    # 清理文件名
                                    clean_filename = re.sub(r'[<>:"/\\|?*]', '_', decoded_filename)

                                    # 添加邮箱前缀避免重名
                                    email_prefix = email_addr.split('@')[0]
                                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
                                    final_filename = f"{email_prefix}_{timestamp}_{clean_filename}"

                                    filepath = os.path.join(download_folder, final_filename)

                                    # 获取附件内容 - 多种方法尝试
                                    payload = None

                                    # 尝试多种方法获取payload
                                    if hasattr(part, 'get_payload'):
                                        try:
                                            payload = part.get_payload(decode=True)
                                            if payload and isinstance(payload, bytes):
                                                print(f"    ✅ 标准方法获取payload成功: {len(payload)} 字节")
                                        except Exception as e:
                                            print(f"    ❌ 标准方法失败: {e}")

                                    # 如果标准方法失败，尝试手动解码
                                    if not payload and hasattr(part, 'get_payload'):
                                        try:
                                            raw_payload = part.get_payload(decode=False)
                                            if raw_payload and isinstance(raw_payload, str):
                                                import base64
                                                payload = base64.b64decode(raw_payload)
                                                print(f"    ✅ 手动解码成功: {len(payload)} 字节")
                                        except Exception as e:
                                            print(f"    ❌ 手动解码失败: {e}")

                                    # 保存文件
                                    if payload and isinstance(payload, bytes) and len(payload) > 0:
                                        try:
                                            with open(filepath, 'wb') as f:
                                                f.write(payload)

                                            file_size = len(payload)
                                            downloaded_count += 1

                                            # 线程安全的计数更新
                                            with download_lock:
                                                total_downloaded += 1
                                                print(f"📥 [{email_addr}] 下载: {clean_filename} ({file_size} 字节)")
                                        except Exception as save_error:
                                            print(f"⚠️ [{email_addr}] 保存文件失败: {save_error}")
                                    else:
                                        print(f"⚠️ [{email_addr}] 无法获取附件内容: {decoded_filename}")
                                else:
                                    print(f"    ❌ 不是Excel文件: {decoded_filename}")

                            except Exception as e:
                                print(f"⚠️ [{email_addr}] 处理附件时出错: {e}")
                                continue

                    except Exception as e:
                        print(f"⚠️ [{email_addr}] 处理邮件part时出错: {e}")
                        continue

                # 如果没有找到任何附件，尝试备用方法
                if not has_any_attachment:
                    print(f"    🔍 尝试备用附件检测方法...")
                    try:
                        # 备用方法: 直接检查邮件的各个部分
                        if email_message.is_multipart():
                            for part in email_message.get_payload():
                                if hasattr(part, 'get_filename'):
                                    filename = part.get_filename()
                                    if filename and filename.lower().endswith(('.xlsx', '.xls', '.xlsm', '.xlsb')):
                                        print(f"    📎 备用方法发现Excel文件: {filename}")

                                        # 尝试获取内容
                                        try:
                                            payload = part.get_payload(decode=True)
                                            if payload and isinstance(payload, bytes):
                                                # 解码文件名
                                                try:
                                                    decoded_parts = email.header.decode_header(filename)
                                                    decoded_filename = ''
                                                    for fname_part, encoding in decoded_parts:
                                                        if isinstance(fname_part, bytes):
                                                            if encoding:
                                                                decoded_filename += fname_part.decode(encoding, errors='ignore')
                                                            else:
                                                                decoded_filename += fname_part.decode('utf-8', errors='ignore')
                                                        else:
                                                            decoded_filename += fname_part
                                                except:
                                                    decoded_filename = filename

                                                # 保存文件
                                                clean_filename = re.sub(r'[<>:"/\\|?*]', '_', decoded_filename)
                                                email_prefix = email_addr.split('@')[0]
                                                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
                                                final_filename = f"{email_prefix}_{timestamp}_{clean_filename}"
                                                filepath = os.path.join(download_folder, final_filename)

                                                with open(filepath, 'wb') as f:
                                                    f.write(payload)

                                                downloaded_count += 1
                                                with download_lock:
                                                    total_downloaded += 1
                                                    print(f"📥 [{email_addr}] 备用方法下载: {clean_filename} ({len(payload)} 字节)")
                                                has_any_attachment = True
                                        except Exception as backup_error:
                                            print(f"    ❌ 备用方法失败: {backup_error}")
                    except Exception as e:
                        print(f"    ❌ 备用方法出错: {e}")

                if not has_any_attachment:
                    print(f"    ❌ 该邮件没有发现任何附件")

            except Exception as e:
                print(f"⚠️ [{email_addr}] 处理邮件时出错: {e}")
                continue

        # 关闭连接
        mail.close()
        mail.logout()

        print(f"✅ [{email_addr}] 完成，下载了 {downloaded_count} 个文件")
        return downloaded_count

    except imaplib.IMAP4.error as e:
        print(f"❌ [{email_addr}] 连接错误: {e}")
        return 0
    except Exception as e:
        print(f"❌ [{email_addr}] 运行错误: {e}")
        return 0

def download_excel_from_all_accounts():
    """从所有配置的邮箱账号下载Excel文件"""
    global total_downloaded
    total_downloaded = 0

    # 加载配置
    config = load_config()

    # 检查配置
    if not config["accounts"]:
        print("❌ 没有配置的邮箱账号！")
        print("💡 请先添加邮箱账号")
        return

    # 创建下载文件夹
    download_folder = config["download_folder"]
    if not os.path.exists(download_folder):
        os.makedirs(download_folder)
        print(f"📁 创建下载文件夹: {download_folder}")

    print(f"🚀 开始多线程下载，共 {len(config['accounts'])} 个邮箱账号")
    print(f"🧵 最大线程数: {config['max_threads']}")
    print("-" * 50)

    start_time = time.time()

    # 使用线程池执行下载
    with ThreadPoolExecutor(max_workers=config["max_threads"]) as executor:
        # 提交所有下载任务
        future_to_account = {
            executor.submit(download_from_single_account, account, config): account
            for account in config["accounts"]
        }

        # 等待所有任务完成
        for future in as_completed(future_to_account):
            account = future_to_account[future]
            try:
                result = future.result()
            except Exception as e:
                print(f"❌ [{account['email']}] 任务异常: {e}")

    end_time = time.time()
    elapsed_time = end_time - start_time

    print("\n" + "=" * 50)
    print(f"🎉 所有下载任务完成！")
    print(f"📊 总共下载: {total_downloaded} 个Excel文件")
    print(f"⏱️  用时: {elapsed_time:.2f} 秒")
    print(f"📁 文件保存在: {os.path.abspath(download_folder)}")
    print("=" * 50)

def show_config():
    """显示当前配置"""
    config = load_config()
    print("\n📋 当前配置信息")
    print("=" * 40)
    print(f"邮箱账号数量: {len(config['accounts'])}")

    if config['accounts']:
        print("已配置的邮箱:")
        for i, account in enumerate(config['accounts'], 1):
            print(f"  {i}. {account['email']}")
    else:
        print("  暂无配置的邮箱")

    print(f"下载文件夹: {config['download_folder']}")
    print(f"搜索天数: {config['search_days']}天")
    print(f"最大线程数: {config['max_threads']}")

def show_menu():
    """显示主菜单"""
    print("\n" + "=" * 50)
    print("🔧 QQ邮箱Excel文件下载器 - 多线程版")
    print("=" * 50)
    print("1. 添加邮箱账号")
    print("2. 删除邮箱账号")
    print("3. 开始下载Excel文件")
    print("4. 查看当前配置")
    print("5. 更新设置")
    print("6. 退出程序")
    print("-" * 50)

def main():
    """主函数"""
    print("💡 获取QQ邮箱授权码方法：")
    print("   1. 登录QQ邮箱网页版")
    print("   2. 设置 → 账户 → POP3/IMAP/SMTP服务")
    print("   3. 开启IMAP/SMTP服务并生成授权码")

    while True:
        show_menu()
        choice = input("请选择操作 (1-6): ").strip()

        if choice == '1':
            add_account()
        elif choice == '2':
            remove_account()
        elif choice == '3':
            config = load_config()
            if not config["accounts"]:
                print("❌ 请先添加邮箱账号！")
                continue

            print(f"\n📧 邮箱账号: {len(config['accounts'])} 个")
            print(f"📅 搜索范围: 最近{config['search_days']}天")
            print(f"📁 下载目录: {config['download_folder']}")
            print(f"🧵 最大线程数: {config['max_threads']}")

            confirm = input("\n确认开始多线程下载？(y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是', '确认']:
                download_excel_from_all_accounts()
            else:
                print("❌ 取消下载")
        elif choice == '4':
            show_config()
        elif choice == '5':
            update_settings()
        elif choice == '6':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请输入1-6")

        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
