#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ邮箱Excel文件下载器 - 菜单版
支持配置管理和下载功能
"""

import imaplib
import email
import os
import re
import json
from datetime import datetime

# ==================== 配置管理 ====================
CONFIG_FILE = "email_config.json"  # 配置文件名

# 默认配置
DEFAULT_CONFIG = {
    "qq_email": "",
    "qq_password": "",
    "download_folder": "downloads",
    "search_days": 30
}

def load_config():
    """加载配置文件"""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            pass
    return DEFAULT_CONFIG.copy()

def save_config(config):
    """保存配置文件"""
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except:
        return False

def update_config():
    """更新配置"""
    print("\n📝 配置QQ邮箱信息")
    print("=" * 40)

    config = load_config()

    # 显示当前配置
    if config["qq_email"]:
        print(f"当前邮箱: {config['qq_email']}")

    # 输入新配置
    email = input("请输入QQ邮箱: ").strip()
    if not email:
        print("❌ 邮箱不能为空")
        return False

    if not email.endswith("@qq.com"):
        print("❌ 请输入正确的QQ邮箱（必须以@qq.com结尾）")
        return False

    password = input("请输入QQ邮箱授权码: ").strip()
    if not password:
        print("❌ 授权码不能为空")
        return False

    # 可选配置
    days = input(f"搜索天数范围 (默认{config['search_days']}天): ").strip()
    if days and days.isdigit():
        config["search_days"] = int(days)

    folder = input(f"下载文件夹 (默认{config['download_folder']}): ").strip()
    if folder:
        config["download_folder"] = folder

    # 保存配置
    config["qq_email"] = email
    config["qq_password"] = password

    if save_config(config):
        print("✅ 配置保存成功！")
        print("\n💡 获取授权码方法：")
        print("   1. 登录QQ邮箱网页版")
        print("   2. 设置 → 账户 → POP3/IMAP/SMTP服务")
        print("   3. 开启IMAP/SMTP服务并生成授权码")
        return True
    else:
        print("❌ 配置保存失败")
        return False

# ==================== 下载功能 ====================

def download_excel_from_qq():
    """从QQ邮箱下载Excel文件"""

    # 加载配置
    config = load_config()

    # 检查配置
    if not config["qq_email"] or not config["qq_password"]:
        print("❌ 请先配置QQ邮箱信息！")
        print("💡 请选择菜单选项1进行配置")
        return

    # 创建下载文件夹
    download_folder = config["download_folder"]
    if not os.path.exists(download_folder):
        os.makedirs(download_folder)
        print(f"📁 创建下载文件夹: {download_folder}")

    try:
        # 连接QQ邮箱
        print("🔗 正在连接QQ邮箱...")
        mail = imaplib.IMAP4_SSL('imap.qq.com')
        mail.login(config["qq_email"], config["qq_password"])
        print("✅ 邮箱连接成功")

        # 选择收件箱
        mail.select('INBOX')

        # 搜索邮件
        from datetime import datetime, timedelta
        since_date = (datetime.now() - timedelta(days=config["search_days"])).strftime("%d-%b-%Y")
        status, messages = mail.search(None, f'SINCE {since_date}')

        if status != 'OK':
            print("❌ 搜索邮件失败")
            return

        email_ids = messages[0].split()
        print(f"📧 找到 {len(email_ids)} 封邮件，正在检查Excel附件...")

        downloaded_count = 0

        # 处理每封邮件
        for i, email_id in enumerate(email_ids, 1):
            try:
                print(f"🔍 检查邮件 {i}/{len(email_ids)}", end='\r')

                # 获取邮件
                status, msg_data = mail.fetch(email_id, '(RFC822)')
                if status != 'OK':
                    continue

                # 解析邮件
                email_message = email.message_from_bytes(msg_data[0][1])

                # 获取邮件信息
                subject = email_message['Subject'] or '无主题'
                if subject:
                    subject = email.header.decode_header(subject)[0][0]
                    if isinstance(subject, bytes):
                        subject = subject.decode('utf-8', errors='ignore')

                sender = email_message['From'] or '未知发件人'
                if sender:
                    sender = email.header.decode_header(sender)[0][0]
                    if isinstance(sender, bytes):
                        sender = sender.decode('utf-8', errors='ignore')

                # 检查附件
                has_excel = False
                for part in email_message.walk():
                    if part.get_content_disposition() == 'attachment':
                        filename = part.get_filename()
                        if filename:
                            # 解码文件名
                            if isinstance(filename, str):
                                pass
                            else:
                                decoded = email.header.decode_header(filename)[0]
                                filename = decoded[0]
                                if isinstance(filename, bytes):
                                    filename = filename.decode(decoded[1] or 'utf-8', errors='ignore')

                            # 检查是否为Excel文件
                            if filename.lower().endswith(('.xlsx', '.xls', '.xlsm', '.xlsb')):
                                has_excel = True

                                # 创建邮件文件夹
                                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                                email_folder = os.path.join(download_folder, f"邮件_{email_id.decode()}_{timestamp}")
                                if not os.path.exists(email_folder):
                                    os.makedirs(email_folder)

                                # 清理文件名
                                clean_filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
                                filepath = os.path.join(email_folder, clean_filename)

                                # 保存附件
                                with open(filepath, 'wb') as f:
                                    f.write(part.get_payload(decode=True))

                                # 保存邮件信息
                                info_file = os.path.join(email_folder, '邮件信息.txt')
                                with open(info_file, 'w', encoding='utf-8') as f:
                                    f.write(f"主题: {subject}\n")
                                    f.write(f"发件人: {sender}\n")
                                    f.write(f"日期: {email_message['Date']}\n")
                                    f.write(f"附件: {clean_filename}\n")

                                downloaded_count += 1
                                print(f"\n📥 下载完成: {clean_filename}")
                                print(f"   主题: {subject}")
                                print(f"   发件人: {sender}")

            except Exception as e:
                print(f"\n⚠️  处理邮件时出错: {e}")
                continue

        # 关闭连接
        mail.close()
        mail.logout()

        print(f"\n🎉 下载完成！")
        print(f"📊 共下载 {downloaded_count} 个Excel文件")
        print(f"📁 文件保存在: {os.path.abspath(download_folder)}")

    except imaplib.IMAP4.error as e:
        print(f"❌ 邮箱连接错误: {e}")
        print("💡 请检查:")
        print("   - QQ邮箱是否开启了IMAP服务")
        print("   - 授权码是否正确（不是QQ密码）")
    except Exception as e:
        print(f"❌ 程序运行错误: {e}")

def show_config():
    """显示当前配置"""
    config = load_config()
    print("\n📋 当前配置信息")
    print("=" * 40)
    print(f"QQ邮箱: {config['qq_email'] if config['qq_email'] else '未配置'}")
    print(f"授权码: {'已配置' if config['qq_password'] else '未配置'}")
    print(f"下载文件夹: {config['download_folder']}")
    print(f"搜索天数: {config['search_days']}天")

def show_menu():
    """显示主菜单"""
    print("\n" + "=" * 50)
    print("🔧 QQ邮箱Excel文件下载器")
    print("=" * 50)
    print("1. 配置QQ邮箱信息")
    print("2. 开始下载Excel文件")
    print("3. 查看当前配置")
    print("4. 退出程序")
    print("-" * 50)

def main():
    """主函数"""
    while True:
        show_menu()
        choice = input("请选择操作 (1-4): ").strip()

        if choice == '1':
            update_config()
        elif choice == '2':
            config = load_config()
            if not config["qq_email"] or not config["qq_password"]:
                print("❌ 请先配置QQ邮箱信息！")
                continue

            print(f"\n📧 邮箱: {config['qq_email']}")
            print(f"📅 搜索范围: 最近{config['search_days']}天")
            print(f"📁 下载目录: {config['download_folder']}")

            confirm = input("\n确认开始下载？(y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是', '确认']:
                download_excel_from_qq()
            else:
                print("❌ 取消下载")
        elif choice == '3':
            show_config()
        elif choice == '4':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请输入1-4")

        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
