import logging
from datetime import datetime
from datetime import timedelta
from pathlib import Path
import shutil

import re
from Executor import Executor
from email.header import decode_header
import email
from imapclient import IMAPClient

logger = logging.getLogger(__name__)


class JobExcelDownloader(Executor):
    def __init__(self, output_directory_path:str, source_email_infos:list[dict[str, str]], from_email: str,
                 since_days:int, imap_server:str, is_debug:bool):
        self._outputDirectory = output_directory_path
        self._source_email_infos = source_email_infos
        self._fromEmail = from_email
        self._sinceDays = since_days
        self._imapServer = imap_server

    def execute(self):
        outputDirectory = Path(self._outputDirectory)
        # 递归删除目录下所有文件
        if outputDirectory.exists():
            shutil.rmtree(outputDirectory)
        
        outputDirectory.mkdir(parents=True, exist_ok=True)

        for sourceEmailInfo in self._source_email_infos:
            username= sourceEmailInfo["username"]
            password = sourceEmailInfo["password"]
            self._download_email(username, password, outputDirectory)

    def _download_email(self, username:str, password:str, outputDirectory:Path):
        # 连接网易邮箱并发送 ID 命令
        with IMAPClient(host=self._imapServer, port=993, ssl=True) as client:
            client.login(username, password)
            client.id_({"name": "IMAPClient", "version": "2.1.0"})  # 关键：网易强制要求[1](@ref)
            client.select_folder('INBOX')

            # 计算时间范围（IMAP 格式：DD-MMM-YYYY）
            since_date = (datetime.now() - timedelta(days=self._sinceDays)).strftime("%d-%b-%Y")
            # 构建搜索条件：发件人 + 时间范围
            messages = client.search(['FROM', self._fromEmail, 'SINCE', since_date])
            count = 0

            for msg_id, msg_data in client.fetch(messages, ['RFC822', 'ENVELOPE']).items():
                count += 1
                raw_email = msg_data[b'RFC822']
                parsedEmail = email.message_from_bytes(raw_email)

                # 校验发件人
                sender = parsedEmail.get('From', '')
                if self._fromEmail not in sender:
                    continue

                # 校验日期（时区敏感处理）
                date_str = parsedEmail.get('Date', '')
                email_date = datetime.strptime(date_str, "%a, %d %b %Y %H:%M:%S %z").astimezone()
                if email_date < (datetime.now() - timedelta(days=self._sinceDays)).astimezone():
                    continue

                # 提取主题并匹配姓名
                subject = email.header.decode_header(parsedEmail['Subject'])[0][0].decode()
                pattern = r'^(?:Fw: )?([\u4e00-\u9fa5]+?)(?:同学)?定制岗位推送岗位更新通知'
                match = re.search(pattern, subject)
                if not match:
                    continue
                name = match.group(1)

                # 创建目标目录
                formatted_date = email_date.strftime("%Y%m%d")
                target_dir = outputDirectory / name
                target_dir.mkdir(parents=True, exist_ok=True)

                # 处理附件（仅保存 .xlsx）
                for part in parsedEmail.walk():
                    if part.get_content_maintype() == 'multipart' or part.get('Content-Disposition') is None:
                        continue

                    filename = self._decode_filename(part.get_filename())
                    if filename.endswith('.xlsx'):
                        save_path = target_dir / f'{name}同学岗位筛选-{formatted_date}.xlsx'
                        with open(save_path, 'wb') as f:
                            f.write(part.get_payload(decode=True))

                logger.info(f"已下载{name}同学的邮件")

            logger.info(f"总共遍历邮件：{count} from {username}")

    def _decode_filename(self, encodedName):
        decoded = decode_header(encodedName)[0]
        if isinstance(decoded[0], bytes):
            return decoded[0].decode(decoded[1] or 'gbk')  # QQ邮箱常用gbk编码
        return decoded[0]