import datetime
import time

import pandas as pd
import pymysql
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 读取Excel文件, 数据要求，第一列为案件编号，第二列为原始号码，第三列为新号码，没有列标题和大标题
df = pd.read_excel(r'C:\Users\<USER>\Desktop\修改\联系电话.xlsx', engine='openpyxl', header=None)

# 初始化新的列表
newID = []
newNumber = []
oldNumber = []

if input("批量修改联系电话，是否确定？y/n") == "y":
    logging.info("开始批量修改")
    time.sleep(1)
else:
    exit()
# 遍历每一行数据
for index, row in df.iterrows():
    id = row[0].replace("\n", "").replace("'", "")
    newID.append(id)

    # 不为空
    if not pd.isna(row[1]):
        try:
            re_compile = re.compile(r"联系电话：(?P<phone>\d{11})", re.S)
            phone = re_compile.search(row[1]).group("phone")
            replace = str(phone).replace("\n", "").replace("'", "")
            oldNumber.append(replace)
        except AttributeError:
            replace = str(row[1]).replace("\n", "").replace("'", "")
            oldNumber.append(replace)
    else:
        oldNumber.append(None)

    # 不为空
    if not pd.isna(row[2]):
        try:
            re_compile = re.compile(r"联系电话：(?P<phone>\d{11})", re.S)
            phone = re_compile.search(row[2]).group("phone")
            replace = str(phone).replace("\n", "").replace("'", "")
            newNumber.append(replace)
        except AttributeError:
            replace = str(row[2]).replace("\n", "").replace("'", "")
            newNumber.append(replace)
    else:
        newNumber.append(None)

time.sleep(1)
for i in range(len(newID)):
    print(f"案件编号: {newID[i]},原始电话: {oldNumber[i]}, 新电话: {newNumber[i]}")
print()
time.sleep(1)

# 数据库连接配置
db_config = {
    "host": "************",
    "user": "user_xzcf",
    "password": "Nmgyjj@2022",
    "database": "lcm_project",
    "port": 3306,
    "charset": "utf8"
}


def get_case_data(cursor, case_no):
    query_sql = """
    SELECT l.PERSON_NAME,l.LINK_NUM,l.ORG_NAME
    FROM litigant l
    JOIN case_source cs ON l.id = cs.litigant_id
    JOIN t_push_case_info_task t ON cs.id = t.case_id
    WHERE t.case_no = %s;
    """
    cursor.execute(query_sql, (case_no))
    return cursor.fetchone()


def update_litigant(cursor, case_no, phone):
    update_sql = """
    UPDATE litigant l
    JOIN case_source cs ON l.id = cs.litigant_id
    JOIN t_push_case_info_task t ON cs.id = t.case_id
    SET l.LINK_NUM = %s
    WHERE t.case_no = %s;
    """
    cursor.execute(update_sql, (phone, case_no))


def get_phone_Number(cursor, phone):
    select_sql = """
    SELECT * FROM litigant WHERE LINK_NUM = %s
    """
    cursor.execute(select_sql, (phone,))
    return cursor.fetchone()


try:
    with pymysql.connect(**db_config) as connection:
        with connection.cursor() as cursor:
            for i in range(len(newID)):
                case_no = newID[i]
                phone = newNumber[i]

                print(f"案件编号: {newID[i]},原始电话: {oldNumber[i]}, 新电话: {newNumber[i]}")
                # 查原数据
                result = get_case_data(cursor, case_no)
                logging.info(f"{case_no}原数据: {result}")
                time.sleep(1)

                if result:
                    # 更新 litigant 表
                    update_litigant(cursor, case_no, phone)
                    time.sleep(1)
                    updated_litigant = get_case_data(cursor, case_no)
                    logging.info(f"{case_no}更新后的 litigant 数据: {updated_litigant}")

                    numbers = get_phone_Number(cursor, phone)
                    logging.info(f"号码{phone}的数据：{numbers}")
                else:
                    logging.warning(f"未找到案件编号: {case_no}")
                time.sleep(1)
                print()

            time.sleep(2)
            if input("人工校验是否通过？y/n") == "y":
                logging.info("提交更改")
                connection.commit()
            else:
                logging.info("取消更改")
                connection.rollback()
except pymysql.MySQLError as e:
    logging.error(f"数据库操作失败: {e}")
