import logging
from datetime import datetime
from pathlib import Path
import openpyxl
from typing import List, Dict, Any

from Executor import Executor
from SmartDocument import (
    SmartBook, SmartSheet, RecordData,
    TextField, NumberField, DateField,
    TencentSmartClient
)

logger = logging.getLogger(__name__)

class ExcelFieldMapping:
    """Excel字段映射配置"""
    def __init__(self, column_name: str, field_name: str, field_type: str = "text",):
        self.column_name = column_name    # Excel表头名称，如 "公司名称"
        self.field_name = field_name      # 智能表格字段名
        self.field_type = field_type      # 字段类型：text, number, date 等
        self.excel_column = ""            # 运行时根据表头确定的列号

class JobExcelUploader(Executor):
    def __init__(self, input_directory: str, client: TencentSmartClient, book_name_pattern: str,
                 sheet_name_pattern: str, field_mappings: List[ExcelFieldMapping], is_debug:bool):
        self._input_directory = input_directory
        self._client = client
        self._book_name_pattern = book_name_pattern
        self._sheet_name_pattern = sheet_name_pattern
        self._field_mappings = field_mappings

    def _find_smart_book(self, name: str) -> SmartBook:
        """根据学生姓名查找对应的智能文档"""
        book_name = self._book_name_pattern.format(name=name)
        books = self._client.search_files(  # 直接使用 client 的 search_files 方法
            searchKey=book_name,
            searchType="name",
            fileTypes="sheet"
        )
        return books[0] if books else None

    def _get_smart_sheet(self, book: SmartBook, name: str) -> SmartSheet:
        """获取指定名称的表格"""
        sheet_name = self._sheet_name_pattern.format(name=name)
        sheets = book.list_sheets()
        for sheet in sheets:
            if sheet.title == sheet_name:
                return sheet
        return None

    def _create_field(self, field_mapping: ExcelFieldMapping, value: Any):
        """根据字段类型创建对应的Field对象"""
        if field_mapping.field_type == "text":
            return TextField(field_mapping.field_name, str(value) if value is not None else "")
        elif field_mapping.field_type == "number":
            try:
                return NumberField(field_mapping.field_name, float(value) if value is not None else 0)
            except (ValueError, TypeError):
                return NumberField(field_mapping.field_name, 0)
        elif field_mapping.field_type == "date":
            if isinstance(value, datetime):
                return DateField(field_mapping.field_name, value)
            return DateField(field_mapping.field_name, None)
        return None

    def _find_header_row(self, worksheet) -> tuple[int, dict[str, str]]:
        """查找表头行并返回列名映射
        
        Returns:
            tuple[int, dict[str, str]]: (表头行号, {列名: 列号})
        """
        max_search_rows = min(10, worksheet.max_row)  # 只在前10行中查找表头
        required_columns = {mapping.column_name for mapping in self._field_mappings}
        
        for row in range(1, max_search_rows + 1):
            found_columns = {}
            for cell in worksheet[row]:
                if not cell.value:  # 跳过空单元格
                    continue
                cell_value = str(cell.value).strip()
                if cell_value in required_columns:
                    found_columns[cell_value] = cell.column_letter
                    
            # 如果找到所有需要的列名，则认为这是表头行
            if set(found_columns.keys()) == required_columns:
                return row, found_columns
                
        raise ValueError("Could not find header row with all required columns")

    def _process_excel_file(self, excel_path: Path, sheet: SmartSheet):
        """处理Excel文件并上传数据"""
        workbook = openpyxl.load_workbook(excel_path, data_only=True)
        worksheet = workbook.active

        try:
            # 查找表头行和列映射
            header_row, column_mapping = self._find_header_row(worksheet)
            
            # 更新字段映射中的列号
            for mapping in self._field_mappings:
                mapping.excel_column = column_mapping.get(mapping.column_name, "")
                if not mapping.excel_column:
                    raise ValueError(f"Column not found: {mapping.column_name}")

            # 从表头行的下一行开始读取数据
            for row in range(header_row + 1, worksheet.max_row + 1):
                fields = []
                for mapping in self._field_mappings:
                    cell = worksheet[f"{mapping.excel_column}{row}"]
                    field = self._create_field(mapping, cell.value)
                    if field:
                        fields.append(field)

                if fields:
                    sheet.add_records(fields)

        finally:
            workbook.close()

    def execute(self):
        """执行上传任务"""
        input_directory = Path(self._input_directory)
        if not input_directory.exists():
            logger.error(f"Input directory not found: {input_directory}")
            return

        # 遍历所有子目录
        for student_dir in input_directory.iterdir():
            if not student_dir.is_dir():
                continue

            student_name = student_dir.name
            logger.info(f"Processing student: {student_name}")

            # 查找对应的智能文档
            book = self._find_smart_book(student_name)
            if not book:
                logger.error(f"Smart book not found for student: {student_name}")
                continue

            # 获取指定的表格
            sheet = self._get_smart_sheet(book, student_name)
            if not sheet:
                formatted_sheet_name = self._sheet_name_pattern.format(name=student_name)
                logger.error(f"Sheet '{formatted_sheet_name}' not found in book: {book.title}")
                continue

            # 处理目录下的所有Excel文件
            for excel_file in student_dir.glob("*.xlsx"):
                logger.info(f"Processing file: {excel_file}")
                try:
                    self._process_excel_file(excel_file, sheet)
                except Exception as e:
                    logger.error(f"Error processing file {excel_file}: {str(e)}") 