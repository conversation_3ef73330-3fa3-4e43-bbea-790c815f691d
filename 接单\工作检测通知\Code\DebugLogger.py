from WecomRobot import WecomRobot
import logging

class DebugLogger:
    def __init__(self, webhook_key: str, logger=None):
        self._wecom_robot = WecomRobot(webhook_key=webhook_key)
        self.logger = logger or logging.getLogger(__name__)

    def log(self, message: str, level=logging.INFO):
        # error、critical、warning和debug级别的消息才发送webhook消息
        if level in [logging.ERROR, logging.CRITICAL, logging.WARNING, logging.DEBUG]:
            if level == logging.ERROR:
                level_name = "ERROR"
            elif level == logging.CRITICAL:
                level_name = "CRITICAL"
            elif level == logging.WARNING:
                level_name = "WARNING"
            else:
                level_name = "DEBUG"
            try:
                self._wecom_robot.send_message(f"[{level_name}] {message}", ["ChengFengSuiYun"])
            except Exception as e:
                self.logger.error(f"发送企业微信消息失败: {str(e)}")
        
        # 同时输出到日志
        if level == logging.INFO:
            self.logger.info(message)
        elif level == logging.ERROR:
            self.logger.error(message)
        elif level == logging.WARNING:
            self.logger.warning(message)
        elif level == logging.CRITICAL:
            self.logger.critical(message)
        elif level == logging.DEBUG:
            self.logger.debug(message)
    
    def info(self, message: str):
        """发送信息级别的日志"""
        self.log(message, level=logging.INFO)
        
    def error(self, message: str):
        """发送错误级别的日志"""
        self.log(message, level=logging.ERROR)
        
    def warning(self, message: str):
        """发送警告级别的日志"""
        self.log(message, level=logging.WARNING)
        
    def critical(self, message: str):
        """发送严重错误级别的日志"""
        self.log(message, level=logging.CRITICAL)
        
    def debug(self, message: str):
        """发送调试级别的日志"""
        self.log(message, level=logging.DEBUG)
