import datetime
import re
import time

import pandas as pd
import pymysql
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 读取Excel文件, 数据要求，第一列为案件编号，第二列为原始日期，第三列为新日期，没有列标题和大标题
df = pd.read_excel(r'C:\Users\<USER>\Desktop\修改\行政处罚日期.xlsx', engine='openpyxl', header=None)

# 初始化新的列表
newID = []
newTime = []
oldTime = []


def TimeFormatting(dataTime):
    date_obj = datetime.datetime.strptime(dataTime, "%Y年%m月%d日")
    timestamp = date_obj.strftime('%Y-%m-%d')
    return timestamp


def Normal(str, value):
    re_compile = re.compile(str, re.S)
    search = re_compile.search(value)
    group = search.group("date")
    return group


# 匹配项数组
NormalString = [
    r"行政处罚决定日期：(?P<date>\d+年\d+月\d+日)；",
    r"行政处罚决定日期(?P<date>\d+年\d+月\d+日)；",
    r"行政处罚决定日期(?P<date>\d+年\d+月\d+日)",
    r"行政处罚决定日期(?P<date>\d{4}-\d{2}-\d{2})",
    r"行政处罚决定日期：(?P<date>\d{4}-\d{2}-\d{2})",
    r"行政处罚决定日期：(?P<date>\d{4}-\d{2}-\d{2})；",
    r"行政处罚决定日期<(?P<date>.*?)>",
    r"处罚决定书日期(?P<date>\d+年\d+月\d+日)",
    r"处罚决定书日期<(?P<date>.*?)>",
    r"处罚决定书日期(?P<date>\d+年\d+月\d+日)",
    r"处罚决定书日期(?P<date>\d{4}-\d{2}-\d{2})"
]


def searchTime(array, value):
    tempValue = value
    for i in NormalString:
        try:
            # 尝试使用re模块匹配日期字符串，失败会抛出异常
            normal = Normal(i, value)
            # 尝试将中文日期字符转换为时间日期
            tempValue = TimeFormatting(normal)
            break
        except ValueError:
            tempValue = normal
            break
        except AttributeError:
            tempValue = value
    array.append(tempValue)


# 遍历每一行数据
for index, row in df.iterrows():
    id = row[0].replace("\n", "").replace("'", "")
    newID.append(id)

    # 尝试将日期字符串转换为日期对象
    try:
        oldTime.append(TimeFormatting(row[1]))
    except ValueError:
        searchTime(oldTime, row[1])

    try:
        newTime.append(TimeFormatting(row[2]))
    except ValueError:
        searchTime(newTime, row[2])

for i in range(len(newID)):
    logging.info(f"案件编号: {newID[i]},原始日期: {oldTime[i]}, 更改日期: {newTime[i]}")

if input("批量修改处罚决定日期，数据校验是否正确？y/n") == "y":
    logging.info("开始批量修改")
else:
    exit()

print("\n")
# 数据库连接配置
db_config = {
    "host": "************",
    "user": "user_xzcf",
    "password": "Nmgyjj@2022",
    "database": "lcm_project",
    "port": 3306,
    "charset": "utf8"
}


def get_case_data(cursor, case_no):
    query_sql = """
    SELECT ID,CASE_SOURCE_ID,ORG_LEADER_SUGGESTION,ORG_LEADER_DATE
    FROM DOC_ADMIN_DEAL_DECISION dadd
    JOIN (
        SELECT case_id
        FROM t_push_case_info_task
        WHERE case_no = %s
        LIMIT 1
    ) AS t ON dadd.case_source_id = t.case_id;
    """
    cursor.execute(query_sql, (case_no,))
    return cursor.fetchone()


def update_admin_case(cursor, case_no, register_time):
    update_sql = """
    UPDATE DOC_ADMIN_DEAL_DECISION dadd
    JOIN (
        SELECT case_id
        FROM t_push_case_info_task
        WHERE case_no = %s
        LIMIT 1
    ) AS t ON dadd.case_source_id = t.case_id
    SET dadd.org_leader_date = %s;
    """
    cursor.execute(update_sql, (case_no, register_time))


def clear_push_info(cursor, case_no):
    update_sql = """
    UPDATE t_push_case_info_task
    SET 
          push_info = CASE 
                        WHEN status = 2 AND push_info IS NOT NULL THEN ''
                        ELSE push_info 
                      END,
          status = CASE 
                  WHEN status = 2 THEN 3 
                  ELSE status 
                 END
    WHERE case_no = %s;
    """
    cursor.execute(update_sql, (case_no,))


def get_push_case_info(cursor, case_no):
    query_sql = """
    SELECT CASE_ID, CASE_NO, CASE_NAME, `STATUS`, PUSH_INFO
    FROM t_push_case_info_task
    WHERE case_no = %s;
    """
    cursor.execute(query_sql, (case_no,))
    return cursor.fetchone()

# try:
#     with pymysql.connect(**db_config) as connection:
#         with connection.cursor() as cursor:
#             for i in range(len(newID)):
#                 case_no = newID[i]
#                 register_time = newTime[i]
#                 print(f"案件编号: {newID[i]},原始日期: {oldTime[i]}, 更改日期: {newTime[i]}")
#
#                 # 查原数据
#                 result = get_case_data(cursor, case_no)
#                 logging.info(f"{case_no}原数据: {result}")
#                 time.sleep(1)
#
#                 if result:
#                     # 更新 DOC_ADMIN_DEAL_DECISION 表
#                     update_admin_case(cursor, case_no, register_time)
#                     updated_admin_case = get_case_data(cursor, case_no)
#                     logging.info(f"{case_no}更新后的 DOC_ADMIN_DEAL_DECISION 数据: {updated_admin_case}")
#                     time.sleep(1)
#
#                     # 清空push_info
#                     clear_push_info(cursor, case_no)
#                     # 检查push_info
#                     updated_push_info = get_push_case_info(cursor, case_no)
#                     logging.info(f"{case_no}更新后的 DOC_ADMIN_DEAL_DECISION 数据: {updated_push_info}")
#                     time.sleep(1)
#                     print()
#                 else:
#                     logging.warning(f"未找到案件编号: {case_no}")
#
#             time.sleep(2)
#             if input("人工校验是否通过？y/n") == "y":
#                 logging.info("提交更改")
#                 connection.commit()
#             else:
#                 logging.info("取消更改")
#                 connection.rollback()
# except pymysql.MySQLError as e:
#     logging.error(f"数据库操作失败: {e}")
